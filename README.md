# Spring Boot Command-Line Application

This is a Spring Boot application with a command-line interface using Picocli.

## Requirements

- Java 17 or higher
- Maven 3.6.3 or higher

## How to Build

```bash
mvn clean package
```

This will create an executable JAR file in the `target` directory.

## How to Run

After building, you can run the application using:

```bash
java -jar target/demo-0.0.1-SNAPSHOT.jar [options] [command] [command options]
```

Or during development, you can use:

```bash
mvn spring-boot:run -Dspring-boot.run.arguments="[options] [command] [command options]"
```

## Available Commands

### Main Command

```bash
# Display help
java -jar target/demo-0.0.1-SNAPSHOT.jar --help

# Run with name parameter
java -jar target/demo-0.0.1-SNAPSHOT.jar --name John
```

### Calculator Commands

```bash
# Addition
java -jar target/demo-0.0.1-SNAPSHOT.jar calc add 1 2 3 4 5

# Subtraction
java -jar target/demo-0.0.1-SNAPSHOT.jar calc subtract 10 2 3

# Multiplication
java -jar target/demo-0.0.1-SNAPSHOT.jar calc multiply 2 3 4

# Division
java -jar target/demo-0.0.1-SNAPSHOT.jar calc divide 10 2
java -jar target/demo-0.0.1-SNAPSHOT.jar calc divide 10 3 --precision 4
```

## Project Structure

- `src/main/java` - Java source files
  - `com.example.demo` - Main application package
  - `com.example.demo.commands` - Command implementations
- `src/main/resources` - Configuration files
- `src/test` - Test files