# 系统架构与模式

## 系统架构
1.  插件化架构设计
2.  核心组件:
    *   `PluginManager`: 负责插件的加载、初始化、和生命周期管理。
    *   `PluginRegistry`: 注册和发现可用插件，提供查询接口。
    *   `DocumentProcessor`: 定义文档处理流水线，串联各个处理阶段。
    *   `AIEnhancer`: 与Spring AI集成，提供内容摘要、关键词提取等AI功能。
3.  分层设计:
    *   核心层: 提供基础框架、SPI接口和核心服务。
    *   插件层: 实现具体的文档格式解析、内容提取和转换逻辑。
    *   服务层: 暴露命令行接口(CLI)或API，供用户或其他系统调用。

## 关键技术决策
1.  使用Java SPI机制实现插件化，实现高可扩展性。
2.  采用责任链模式构建文档处理流水线，实现灵活的流程定制。
3.  索引构建采用倒排索引结构，优化查询性能。
4.  通过异步处理和并发控制，提高文档处理吞吐量。
5.  集成Spring AI进行内容增强，提升数据价值。
6.  使用Picocli构建功能丰富的命令行接口。

## 设计模式
1.  **工厂模式**: 用于插件实例的创建，解耦插件的实例化过程。
2.  **观察者模式**: 用于插件状态变更的通知，例如热加载或卸载。
3.  **策略模式**: 用于支持不同的文档处理算法，如不同的Markdown转换策略。
4.  **装饰器模式**: 用于动态地为文档处理流水线增加功能。
5.  **责任链模式**: 用于组织文档处理的各个步骤，如内容提取、AI增强、元数据处理等。
6.  **适配器模式**: 用于集成不同格式的第三方转换器。

## 组件关系
```mermaid
graph TD
    subgraph Service Layer
        CLI[命令行接口 (Picocli)]
    end

    subgraph Core Layer
        CLI --> DP[DocumentProcessor]
        DP --> PM[PluginManager]
        PM --> PR[PluginRegistry]
        DP --> Chain[责任链 (Handler1, Handler2, ...)]
    end

    subgraph Plugin Layer
        PR -- "查找插件" --> Plugins
        subgraph Plugins
            direction LR
            P_PDF[PDF插件]
            P_DOCX[DOCX插件]
            P_HTML[HTML插件]
            P_TXT[文本插件]
        end
        Chain -- "调用插件" --> Plugins
    end

    subgraph AI Integration
        Chain --> AIE[AIEnhancer]
        AIE --> SAI[Spring AI]
    end
```

## 核心处理流程
1.  **文件扫描与过滤**: 根据配置扫描指定目录，过滤不符合条件的文件。
2.  **格式检测与路由**: 检测文件类型，并将任务路由到对应的处理插件。
3.  **文档内容提取**: 插件提取文本、图片和元数据。
4.  **AI增强处理**: (可选) 调用Spring AI服务进行内容摘要、关键词提取等。
5.  **元数据提取**: 提取作者、创建日期等标准元数据。
6.  **结果规范化输出**: 将处理结果统一格式化为Markdown。

## 错误处理与报告
- **统一异常体系**: 定义业务异常基类，所有插件和组件抛出可识别的异常。
- **重试机制**: 对网络请求、AI服务调用等不稳定操作，实现可配置的重试策略（如指数退避）。
- **错误日志**: 记录详细的错误信息，包括堆栈跟踪、上下文信息（如处理的文件名）。
- **失败报告**: 任务执行结束后，生成处理报告，明确列出成功、失败和跳过的文件列表及其原因。
