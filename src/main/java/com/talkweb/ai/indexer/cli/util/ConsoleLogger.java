package com.talkweb.ai.indexer.cli.util;

import java.io.PrintStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 控制台日志工具类
 */
public class ConsoleLogger {
    
    /**
     * 日志级别
     */
    public enum Level {
        DEBUG(0, ConsoleColors.PURPLE),
        INFO(1, ConsoleColors.CYAN),
        SUCCESS(2, ConsoleColors.GREEN),
        WARNING(3, ConsoleColors.YELLOW),
        ERROR(4, ConsoleColors.RED);
        
        private final int value;
        private final String color;
        
        Level(int value, String color) {
            this.value = value;
            this.color = color;
        }
        
        public int getValue() {
            return value;
        }
        
        public String getColor() {
            return color;
        }
    }
    
    private final PrintStream out;
    private final PrintStream err;
    private Level level = Level.INFO;
    private boolean showTimestamp = true;
    private final DateTimeFormatter timestampFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 创建一个控制台日志工具
     * @param out 标准输出流
     * @param err 错误输出流
     */
    public ConsoleLogger(PrintStream out, PrintStream err) {
        this.out = out;
        this.err = err;
    }
    
    /**
     * 创建一个控制台日志工具，使用标准输出和错误输出
     */
    public ConsoleLogger() {
        this(System.out, System.err);
    }
    
    /**
     * 设置日志级别
     * @param level 日志级别
     */
    public void setLevel(Level level) {
        this.level = level;
    }
    
    /**
     * 设置是否显示时间戳
     * @param showTimestamp 是否显示时间戳
     */
    public void setShowTimestamp(boolean showTimestamp) {
        this.showTimestamp = showTimestamp;
    }
    
    /**
     * 输出调试信息
     * @param message 消息
     */
    public void debug(String message) {
        log(Level.DEBUG, message);
    }
    
    /**
     * 输出调试信息
     * @param format 格式化字符串
     * @param args 参数
     */
    public void debug(String format, Object... args) {
        log(Level.DEBUG, String.format(format, args));
    }
    
    /**
     * 输出普通信息
     * @param message 消息
     */
    public void info(String message) {
        log(Level.INFO, message);
    }
    
    /**
     * 输出普通信息
     * @param format 格式化字符串
     * @param args 参数
     */
    public void info(String format, Object... args) {
        log(Level.INFO, String.format(format, args));
    }
    
    /**
     * 输出成功信息
     * @param message 消息
     */
    public void success(String message) {
        log(Level.SUCCESS, message);
    }
    
    /**
     * 输出成功信息
     * @param format 格式化字符串
     * @param args 参数
     */
    public void success(String format, Object... args) {
        log(Level.SUCCESS, String.format(format, args));
    }
    
    /**
     * 输出警告信息
     * @param message 消息
     */
    public void warning(String message) {
        log(Level.WARNING, message);
    }
    
    /**
     * 输出警告信息
     * @param format 格式化字符串
     * @param args 参数
     */
    public void warning(String format, Object... args) {
        log(Level.WARNING, String.format(format, args));
    }
    
    /**
     * 输出错误信息
     * @param message 消息
     */
    public void error(String message) {
        log(Level.ERROR, message);
    }
    
    /**
     * 输出错误信息
     * @param format 格式化字符串
     * @param args 参数
     */
    public void error(String format, Object... args) {
        log(Level.ERROR, String.format(format, args));
    }
    
    /**
     * 输出错误信息和异常堆栈
     * @param message 消息
     * @param throwable 异常
     */
    public void error(String message, Throwable throwable) {
        log(Level.ERROR, message);
        throwable.printStackTrace(err);
    }
    
    /**
     * 输出日志
     * @param level 日志级别
     * @param message 消息
     */
    private void log(Level level, String message) {
        if (level.getValue() < this.level.getValue()) {
            return;
        }
        
        StringBuilder sb = new StringBuilder();
        
        // 添加时间戳
        if (showTimestamp) {
            sb.append(ConsoleColors.wrap(
                LocalDateTime.now().format(timestampFormat),
                ConsoleColors.WHITE
            )).append(" ");
        }
        
        // 添加日志级别
        sb.append(ConsoleColors.wrap(
            String.format("[%s]", level.name()),
            level.getColor()
        )).append(" ");
        
        // 添加消息
        sb.append(message);
        
        // 输出日志
        if (level == Level.ERROR) {
            err.println(sb.toString());
        } else {
            out.println(sb.toString());
        }
    }
}