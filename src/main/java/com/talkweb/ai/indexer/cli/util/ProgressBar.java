package com.talkweb.ai.indexer.cli.util;

import java.io.PrintStream;
import java.text.DecimalFormat;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 命令行进度条工具类
 */
public class ProgressBar {
    private final int total;
    private final AtomicInteger current = new AtomicInteger(0);
    private final PrintStream out;
    private final int width;
    private final String taskName;
    private final long startTime;
    private final DecimalFormat df = new DecimalFormat("#0.0");
    private boolean finished = false;

    /**
     * 创建一个进度条
     * @param taskName 任务名称
     * @param total 总任务数
     * @param width 进度条宽度
     * @param out 输出流
     */
    public ProgressBar(String taskName, int total, int width, PrintStream out) {
        this.taskName = taskName;
        this.total = total;
        this.width = width;
        this.out = out;
        this.startTime = System.currentTimeMillis();
        
        // 初始显示
        update(0);
    }

    /**
     * 创建一个进度条，使用标准输出
     * @param taskName 任务名称
     * @param total 总任务数
     */
    public ProgressBar(String taskName, int total) {
        this(taskName, total, 50, System.out);
    }

    /**
     * 更新进度
     * @param value 当前进度值
     */
    public synchronized void update(int value) {
        if (finished) return;
        
        current.set(value);
        
        // 计算进度百分比
        double percent = (double) value / total;
        int completed = (int) (width * percent);
        
        // 计算已用时间
        long elapsedTime = System.currentTimeMillis() - startTime;
        String timeStr = formatTime(elapsedTime);
        
        // 构建进度条
        StringBuilder sb = new StringBuilder();
        sb.append('\r');
        sb.append(taskName).append(": [");
        for (int i = 0; i < width; i++) {
            sb.append(i < completed ? "=" : i == completed ? ">" : " ");
        }
        sb.append("] ");
        sb.append(df.format(percent * 100)).append("% ");
        sb.append("(").append(value).append("/").append(total).append(") ");
        sb.append(timeStr);
        
        // 输出进度条
        out.print(sb);
        
        // 如果完成，换行
        if (value >= total) {
            out.println();
            finished = true;
        }
    }

    /**
     * 步进进度
     */
    public void step() {
        update(current.incrementAndGet());
    }

    /**
     * 完成进度
     */
    public void complete() {
        update(total);
    }

    /**
     * 格式化时间
     * @param millis 毫秒数
     * @return 格式化后的时间字符串
     */
    private String formatTime(long millis) {
        long seconds = millis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        if (hours > 0) {
            return String.format("%02d:%02d:%02d", hours, minutes % 60, seconds % 60);
        } else {
            return String.format("%02d:%02d", minutes, seconds % 60);
        }
    }
}