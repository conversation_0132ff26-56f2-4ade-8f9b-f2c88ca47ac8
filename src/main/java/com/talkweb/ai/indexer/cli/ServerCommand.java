package com.talkweb.ai.indexer.cli;

import org.springframework.stereotype.Component;
import picocli.CommandLine.Command;
import picocli.CommandLine.ParentCommand;

import java.util.concurrent.Callable;

@Component
@Command(
    name = "server",
    description = "Run the application as a web server",
    mixinStandardHelpOptions = true
)
public class ServerCommand implements Callable<Integer> {

    @ParentCommand
    private DocConverterCommand parent;

    @Override
    public Integer call() throws Exception {
        System.out.println("Server mode is not yet implemented.");
        // Here we would start the web server components
        // For now, we just wait indefinitely to simulate a running server
        Thread.currentThread().join();
        return 0;
    }
}
