package com.talkweb.ai.indexer.core;

import java.util.Objects;

/**
 * 插件元数据
 */

public class PluginMetadata {
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String id;
        private String name;
        private String version;
        private String description = "";
        private String provider = "";
        private String className;

        public Builder id(String id) {
            this.id = id;
            return this;
        }

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public Builder version(String version) {
            this.version = version;
            return this;
        }

        public Builder description(String description) {
            this.description = description;
            return this;
        }

        public Builder provider(String provider) {
            this.provider = provider;
            return this;
        }

        public Builder className(String className) {
            this.className = className;
            return this;
        }

        public PluginMetadata build() {
            return new PluginMetadata(id, name, version, description, provider, className);
        }
    }

    private final String id;
    private final String name;
    private final String version;
    private final String description;
    private final String provider;
    private final String className;

    public PluginMetadata(String id, String name, String version, String description, String provider, String className) {
        this.id = Objects.requireNonNull(id, "Plugin ID cannot be null");
        this.name = Objects.requireNonNull(name, "Plugin name cannot be null");
        this.version = Objects.requireNonNull(version, "Plugin version cannot be null");
        this.description = description != null ? description : "";
        this.provider = provider != null ? provider : "";
        this.className = Objects.requireNonNull(className, "Plugin class name cannot be null");
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getVersion() {
        return version;
    }

    public String getDescription() {
        return description;
    }

    public String getProvider() {
        return provider;
    }

    public String getClassName() {
        return className;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PluginMetadata that = (PluginMetadata) o;
        return id.equals(that.id) && 
               version.equals(that.version);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, version);
    }

    @Override
    public String toString() {
        return "PluginMetadata{" +
               "id='" + id + '\'' +
               ", name='" + name + '\'' +
               ", version='" + version + '\'' +
               ", description='" + description + '\'' +
               ", provider='" + provider + '\'' +
               ", className='" + className + '\'' +
               '}';
    }
}
