package com.talkweb.ai.indexer.core;

/**
 * 文档处理异常
 */
public class DocumentProcessingException extends Exception {
    private final String documentId;
    private final String documentType;
    private final ProcessingErrorCode errorCode;

    public DocumentProcessingException(String message) {
        this(message, null, null, null, null);
    }

    public DocumentProcessingException(String message, Throwable cause) {
        this(message, cause, null, null, null);
    }

    public DocumentProcessingException(String message, String documentId, String documentType) {
        this(message, null, documentId, documentType, null);
    }

    public DocumentProcessingException(String message, Throwable cause, String documentId, 
                                     String documentType, ProcessingErrorCode errorCode) {
        super(message, cause);
        this.documentId = documentId;
        this.documentType = documentType;
        this.errorCode = errorCode != null ? errorCode : ProcessingErrorCode.UNKNOWN_ERROR;
    }

    public String getDocumentId() {
        return documentId;
    }

    public String getDocumentType() {
        return documentType;
    }

    public ProcessingErrorCode getErrorCode() {
        return errorCode;
    }

    /**
     * 处理错误码枚举
     */
    public enum ProcessingErrorCode {
        /** 未知错误 */
        UNKNOWN_ERROR,
        /** 不支持的文档格式 */
        UNSUPPORTED_FORMAT,
        /** 文档损坏 */
        CORRUPTED_DOCUMENT,
        /** 权限不足 */
        PERMISSION_DENIED,
        /** 资源不足 */
        INSUFFICIENT_RESOURCES,
        /** 超时 */
        TIMEOUT,
        /** 配置错误 */
        CONFIGURATION_ERROR,
        /** 依赖项缺失 */
        MISSING_DEPENDENCY,
        /** 版本不兼容 */
        VERSION_MISMATCH,
        /** 输入/输出错误 */
        IO_ERROR
    }
}
