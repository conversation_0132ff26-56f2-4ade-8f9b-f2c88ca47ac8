package com.talkweb.ai.indexer.core;

import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;

/**
 * 文档处理上下文，包含处理过程中需要的各种参数和配置
 */
public class ProcessingContext {
    // 常量键名
    public static final String OUTPUT_PATH = "outputPath";
    public static final String FORCE = "force";
    public static final String METADATA = "metadata";
    public static final String ORIGINAL_FILENAME = "originalFilename";
    public static final String PRESERVE_IMAGES = "preserveImages";
    public static final String EXTRACT_METADATA = "extractMetadata";
    public static final String EXTRACT_IMAGES = "extractImages";
    public static final String EXTRACT_TABLES = "extractTables";
    public static final String HTML_CONVERSION_MODE = "htmlConversionMode";

    private final Map<String, Object> attributes;

    private ProcessingContext(Builder builder) {
        this.attributes = new HashMap<>(builder.attributes);
    }

    /**
     * 获取属性值
     * @param key 属性键
     * @return 属性值
     */
    public Object getAttribute(String key) {
        return attributes.get(key);
    }

    /**
     * 获取属性值，如果不存在则返回默认值
     * @param key 属性键
     * @param defaultValue 默认值
     * @return 属性值
     */
    public Object getAttribute(String key, Object defaultValue) {
        return attributes.getOrDefault(key, defaultValue);
    }

    /**
     * 获取布尔属性值
     * @param key 属性键
     * @param defaultValue 默认值
     * @return 布尔属性值
     */
    public boolean getBooleanAttribute(String key, boolean defaultValue) {
        Object value = attributes.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return defaultValue;
    }

    /**
     * 获取字符串属性值
     * @param key 属性键
     * @param defaultValue 默认值
     * @return 字符串属性值
     */
    public String getStringAttribute(String key, String defaultValue) {
        Object value = attributes.get(key);
        if (value != null) {
            return value.toString();
        }
        return defaultValue;
    }

    /**
     * 获取输出路径
     * @return 输出路径
     */
    public Path getOutputPath() {
        return (Path) getAttribute(OUTPUT_PATH);
    }

    /**
     * 是否强制覆盖已存在的文件
     * @return 是否强制覆盖
     */
    public boolean isForce() {
        return getBooleanAttribute(FORCE, false);
    }

    /**
     * 是否保留图片
     * @return 是否保留图片
     */
    public boolean isPreserveImages() {
        return getBooleanAttribute(PRESERVE_IMAGES, true);
    }

    /**
     * 是否提取元数据
     * @return 是否提取元数据
     */
    public boolean isExtractMetadata() {
        return getBooleanAttribute(EXTRACT_METADATA, true);
    }

    /**
     * 是否提取图片
     * @return 是否提取图片
     */
    public boolean isExtractImages() {
        return getBooleanAttribute(EXTRACT_IMAGES, true);
    }

    /**
     * 是否提取表格
     * @return 是否提取表格
     */
    public boolean isExtractTables() {
        return getBooleanAttribute(EXTRACT_TABLES, true);
    }

    /**
     * 获取HTML转换模式
     * @return HTML转换模式
     */
    public com.talkweb.ai.indexer.util.HtmlConversionMode getHtmlConversionMode() {
        return (com.talkweb.ai.indexer.util.HtmlConversionMode) getAttribute(HTML_CONVERSION_MODE, com.talkweb.ai.indexer.util.HtmlConversionMode.LOOSE);
    }

    /**
     * 处理上下文构建器
     */
    public static class Builder {
        private final Map<String, Object> attributes = new HashMap<>();

        /**
         * 设置属性
         * @param key 属性键
         * @param value 属性值
         * @return 构建器
         */
        public Builder attribute(String key, Object value) {
            attributes.put(key, value);
            return this;
        }

        /**
         * 设置输出路径
         * @param outputPath 输出路径
         * @return 构建器
         */
        public Builder setOutputPath(Path outputPath) {
            return attribute(OUTPUT_PATH, outputPath);
        }

        /**
         * 设置是否强制覆盖已存在的文件
         * @param force 是否强制覆盖
         * @return 构建器
         */
        public Builder setForce(boolean force) {
            return attribute(FORCE, force);
        }

        /**
         * 设置是否保留图片
         * @param preserveImages 是否保留图片
         * @return 构建器
         */
        public Builder setPreserveImages(boolean preserveImages) {
            return attribute(PRESERVE_IMAGES, preserveImages);
        }

        /**
         * 设置是否提取元数据
         * @param extractMetadata 是否提取元数据
         * @return 构建器
         */
        public Builder setExtractMetadata(boolean extractMetadata) {
            return attribute(EXTRACT_METADATA, extractMetadata);
        }

        /**
         * 设置是否提取图片
         * @param extractImages 是否提取图片
         * @return 构建器
         */
        public Builder setExtractImages(boolean extractImages) {
            return attribute(EXTRACT_IMAGES, extractImages);
        }

        /**
         * 设置是否提取表格
         * @param extractTables 是否提取表格
         * @return 构建器
         */
        public Builder setExtractTables(boolean extractTables) {
            return attribute(EXTRACT_TABLES, extractTables);
        }

        /**
         * 设置HTML转换模式
         * @param mode HTML转换模式
         * @return 构建器
         */
        public Builder setHtmlConversionMode(com.talkweb.ai.indexer.util.HtmlConversionMode mode) {
            return attribute(HTML_CONVERSION_MODE, mode);
        }

        /**
         * 构建处理上下文
         * @return 处理上下文
         */
        public ProcessingContext build() {
            return new ProcessingContext(this);
        }
    }

    /**
     * 创建构建器
     * @return 构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 基于现有上下文创建构建器
     * @param context 现有上下文
     * @return 构建器
     */
    public static Builder builder(ProcessingContext context) {
        Builder builder = new Builder();
        builder.attributes.putAll(context.attributes);
        return builder;
    }
}
