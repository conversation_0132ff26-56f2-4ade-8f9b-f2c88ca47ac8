package com.talkweb.ai.indexer.core;

import java.io.File;
import java.util.Map;

/**
 * 文档处理器接口，所有文档处理插件必须实现此接口
 */
public interface DocumentProcessor extends Plugin {
    
    /**
     * 获取支持的文档扩展名（不包含点）
     * @return 支持的扩展名数组
     */
    String[] getSupportedExtensions();
    
    /**
     * 检查是否支持指定的文件类型
     * @param extension 文件扩展名（不包含点）
     * @return 如果支持则返回true，否则返回false
     */
    boolean supports(String extension);
    
    /**
     * 处理文档
     * @param inputFile 输入文件
     * @param context 处理上下文
     * @return 处理结果
     * @throws DocumentProcessingException 如果处理过程中发生错误
     */
    ProcessingResult process(File inputFile, ProcessingContext context) 
            throws DocumentProcessingException;
    
    /**
     * 获取处理器配置
     * @return 配置映射
     */
    Map<String, Object> getConfiguration();
    
    /**
     * 设置处理器配置
     * @param config 配置映射
     */
    void configure(Map<String, Object> config);
}
