package com.talkweb.ai.indexer.core;

/**
 * 插件异常基类
 */
public class PluginException extends Exception {
    private final PluginState state;

    public PluginException(String message) {
        this(message, null, null);
    }

    public PluginException(String message, Throwable cause) {
        this(message, cause, null);
    }

    public PluginException(String message, PluginState state) {
        this(message, null, state);
    }

    public PluginException(String message, Throwable cause, PluginState state) {
        super(message, cause);
        this.state = state;
    }

    public PluginState getState() {
        return state;
    }
}
