

package com.talkweb.ai.indexer.core;

/**
 * 文档转换结果封装类
 */
public class ConversionResult {
    public enum Status {
        SUCCESS,
        FAILED,
        PARTIAL_SUCCESS
    }

    private final Status status;
    private final String content;
    private final boolean success;
    private final String message;

    public ConversionResult(Status status, String content, String message) {
        this.status = status;
        this.content = content;
        this.success = status == Status.SUCCESS || status == Status.PARTIAL_SUCCESS;
        this.message = message;
    }

    public ConversionResult(String content, boolean success, String message) {
        this(success ? Status.SUCCESS : Status.FAILED, content, message);
    }

    public ConversionResult(Status status, String inputPath, String outputPath, String content) {
        this(status, content, outputPath);
    }

    public Status getStatus() {
        return status;
    }

    // Getters
    public String getContent() { return content; }
    public String getMarkdownContent() { return content; }
    public boolean isSuccess() { return success; }
    public String getMessage() { return message; }
}

