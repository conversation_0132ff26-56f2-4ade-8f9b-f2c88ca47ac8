package com.talkweb.ai.indexer.core;

import java.util.Collection;
import java.util.Optional;

/**
 * 插件注册表接口，负责插件的注册和查找
 */
public interface PluginRegistry {
    
    /**
     * 注册插件
     * @param plugin 要注册的插件
     * @throws PluginException 如果插件已存在或注册失败
     */
    void register(Plugin plugin) throws PluginException;
    
    /**
     * 取消注册插件
     * @param pluginId 插件ID
     * @return 如果插件存在并成功取消注册则返回true
     */
    boolean unregister(String pluginId);
    
    /**
     * 获取所有已注册的插件
     * @return 插件集合
     */
    Collection<Plugin> getAllPlugins();
    
    /**
     * 根据ID获取插件
     * @param pluginId 插件ID
     * @return 插件实例，如果不存在则返回空
     */
    Optional<Plugin> getPlugin(String pluginId);
    
    /**
     * 获取指定类型的所有插件
     * @param pluginType 插件类型
     * @param <T> 插件类型
     * @return 插件集合
     */
    <T extends Plugin> Collection<T> getPluginsByType(Class<T> pluginType);
    
    /**
     * 获取指定类型的插件
     * @param pluginType 插件类型
     * @param pluginId 插件ID
     * @param <T> 插件类型
     * @return 插件实例，如果不存在则返回空
     */
    <T extends Plugin> Optional<T> getPlugin(Class<T> pluginType, String pluginId);
    
    /**
     * 检查插件是否存在
     * @param pluginId 插件ID
     * @return 如果存在则返回true
     */
    boolean hasPlugin(String pluginId);
    
    /**
     * 清空注册表
     */
    void clear();
    
    /**
     * 获取插件数量
     * @return 插件数量
     */
    int size();
}
