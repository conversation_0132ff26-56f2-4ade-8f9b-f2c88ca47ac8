package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.*;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentInformation;
import org.apache.pdfbox.pdmodel.encryption.InvalidPasswordException;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * Enhanced PDF to Markdown converter with improved compatibility, structure preservation,
 * and page-by-page conversion support.
 */
public class PdfToMarkdownConverter implements DocumentConverter, Plugin {

    private static final Logger logger = Logger.getLogger(PdfToMarkdownConverter.class.getName());

    private final PluginMetadata metadata;
    private PluginState state = PluginState.STOPPED;
    private PdfConversionConfig config = new PdfConversionConfig();

    /**
     * Initializes the converter with the provided metadata.
     */
    public PdfToMarkdownConverter(PluginMetadata metadata) {
        this.metadata = metadata;
    }

    /**
     * Configuration class for PDF conversion settings
     */
    public static class PdfConversionConfig {
        private boolean splitByPages = true;
        private boolean preserveStructure = true;
        private boolean extractImages = false;
        private boolean handleEncrypted = true;
        private String defaultPassword = "";
        private int maxPages = Integer.MAX_VALUE;
        private boolean includeMetadata = true;

        // Getters and setters
        public boolean isSplitByPages() { return splitByPages; }
        public void setSplitByPages(boolean splitByPages) { this.splitByPages = splitByPages; }

        public boolean isPreserveStructure() { return preserveStructure; }
        public void setPreserveStructure(boolean preserveStructure) { this.preserveStructure = preserveStructure; }

        public boolean isExtractImages() { return extractImages; }
        public void setExtractImages(boolean extractImages) { this.extractImages = extractImages; }

        public boolean isHandleEncrypted() { return handleEncrypted; }
        public void setHandleEncrypted(boolean handleEncrypted) { this.handleEncrypted = handleEncrypted; }

        public String getDefaultPassword() { return defaultPassword; }
        public void setDefaultPassword(String defaultPassword) { this.defaultPassword = defaultPassword; }

        public int getMaxPages() { return maxPages; }
        public void setMaxPages(int maxPages) { this.maxPages = maxPages; }

        public boolean isIncludeMetadata() { return includeMetadata; }
        public void setIncludeMetadata(boolean includeMetadata) { this.includeMetadata = includeMetadata; }
    }

    @Override
    public void init(PluginContext context) {
        state = PluginState.READY;
    }

    @Override
    public void start() {
        state = PluginState.RUNNING;
    }

    @Override
    public void stop() {
        state = PluginState.STOPPED;
    }

    @Override
    public void destroy() {
        state = PluginState.STOPPED;
    }

    @Override
    public PluginMetadata getMetadata() {
        return metadata;
    }

    @Override
    public PluginState getState() {
        return state;
    }

    /**
     * Configure the PDF converter with custom settings
     */
    public void configure(PdfConversionConfig config) {
        this.config = config;
        logger.info("PDF converter configured with: splitByPages=" + config.isSplitByPages() +
                   ", preserveStructure=" + config.isPreserveStructure());
    }

    /**
     * Get current configuration
     */
    public PdfConversionConfig getConfiguration() {
        return config;
    }

    /**
     * Checks if the converter supports the given file extension.
     */
    @Override
    public boolean supportsExtension(String fileExtension) {
        return "pdf".equalsIgnoreCase(fileExtension);
    }

    /**
     * Converts the provided PDF file to Markdown format with enhanced features.
     */
    @Override
    public ConversionResult convert(File inputFile) throws ConversionException {
        if (inputFile == null || !inputFile.exists() || inputFile.length() == 0) {
            throw new ConversionException("Input file is invalid, empty, or does not exist.");
        }

        logger.info("Starting PDF conversion for: " + inputFile.getName());

        try {
            // Try to load PDF with enhanced error handling
            PDDocument document = loadPdfWithRetry(inputFile);

            if (config.isSplitByPages()) {
                return convertToSeparatePages(document, inputFile);
            } else {
                return convertToSingleFile(document, inputFile);
            }

        } catch (PdfConversionException e) {
            logger.log(Level.SEVERE, "PDF conversion failed: " + e.getDetailedMessage(), e);
            throw new ConversionException(e.getDetailedMessage(), e);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Unexpected error during PDF conversion: " + inputFile.getName(), e);
            throw new ConversionException("Unexpected error during PDF conversion: " + e.getMessage(), e);
        }
    }

    /**
     * Load PDF with enhanced error handling and retry logic
     */
    private PDDocument loadPdfWithRetry(File inputFile) throws PdfConversionException {
        String fileName = inputFile.getName();

        if (!inputFile.exists()) {
            throw PdfConversionException.fileNotFound(fileName);
        }

        try {
            // First attempt: normal loading
            return Loader.loadPDF(inputFile);
        } catch (InvalidPasswordException e) {
            if (config.isHandleEncrypted() && !config.getDefaultPassword().isEmpty()) {
                try {
                    logger.info("Attempting to open encrypted PDF with default password: " + fileName);
                    return Loader.loadPDF(inputFile, config.getDefaultPassword());
                } catch (InvalidPasswordException retryException) {
                    throw PdfConversionException.encryptedFile(fileName);
                } catch (Exception retryException) {
                    throw PdfConversionException.ioError(fileName, retryException);
                }
            } else {
                throw PdfConversionException.encryptedFile(fileName);
            }
        } catch (IOException e) {
            // Check if it's a corrupted file
            if (e.getMessage().contains("corrupt") || e.getMessage().contains("invalid") ||
                e.getMessage().contains("malformed")) {
                throw PdfConversionException.corruptedFile(fileName, e);
            }

            // Try to handle other IO errors with recovery
            logger.warning("Standard PDF loading failed, attempting recovery for: " + fileName + " - " + e.getMessage());
            try {
                // Attempt to load with more lenient settings
                return Loader.loadPDF(inputFile);
            } catch (Exception retryException) {
                throw PdfConversionException.ioError(fileName, retryException);
            }
        } catch (OutOfMemoryError e) {
            throw PdfConversionException.memoryError(fileName, -1);
        }
    }

    /**
     * Convert PDF to separate markdown files for each page
     */
    private ConversionResult convertToSeparatePages(PDDocument document, File inputFile) throws ConversionException {
        try {
            String baseFileName = inputFile.getName().replaceFirst("\\.[^.]+$", "");
            Path outputDir = Paths.get(inputFile.getParent(), baseFileName + "_pages");
            Files.createDirectories(outputDir);

            int totalPages = document.getNumberOfPages();
            int pagesToProcess = Math.min(totalPages, config.getMaxPages());

            List<String> pageFiles = new ArrayList<>();
            StringBuilder combinedContent = new StringBuilder();

            // Add document metadata if enabled
            if (config.isIncludeMetadata()) {
                String metadata = extractDocumentMetadata(document);
                combinedContent.append(metadata).append("\n\n");
            }

            for (int pageNum = 0; pageNum < pagesToProcess; pageNum++) {
                try {
                    EnhancedMarkdownPdfTextStripper stripper = new EnhancedMarkdownPdfTextStripper(pageNum + 1, pageNum + 1);
                    String pageContent = stripper.getText(document);

                    // Create individual page file
                    String pageFileName = String.format("page_%03d.md", pageNum + 1);
                    Path pageFilePath = outputDir.resolve(pageFileName);

                    String pageHeader = String.format("# Page %d\n\n", pageNum + 1);
                    String fullPageContent = pageHeader + pageContent;

                    Files.write(pageFilePath, fullPageContent.getBytes());
                    pageFiles.add(pageFilePath.toString());

                    // Add to combined content
                    combinedContent.append(fullPageContent).append("\n\n---\n\n");

                    logger.info(String.format("Processed page %d/%d", pageNum + 1, pagesToProcess));

                } catch (Exception e) {
                    logger.warning(String.format("Failed to process page %d: %s", pageNum + 1, e.getMessage()));
                    // Continue with next page
                }
            }

            // Create summary file with all pages
            Path summaryFile = outputDir.resolve("_all_pages.md");
            Files.write(summaryFile, combinedContent.toString().getBytes());

            document.close();

            logger.info(String.format("Successfully converted %d pages to %s", pagesToProcess, outputDir));

            return new ConversionResult(
                ConversionResult.Status.SUCCESS,
                inputFile.getPath(),
                summaryFile.toString(),
                combinedContent.toString()
            );

        } catch (IOException e) {
            throw new ConversionException("Failed to create page files: " + e.getMessage(), e);
        }
    }

    /**
     * Convert PDF to a single markdown file
     */
    private ConversionResult convertToSingleFile(PDDocument document, File inputFile) throws ConversionException {
        try {
            Path outputPath = Paths.get(inputFile.getParent(), inputFile.getName() + ".md");

            EnhancedMarkdownPdfTextStripper stripper = new EnhancedMarkdownPdfTextStripper();
            String markdown = stripper.getText(document);

            // Add document metadata if enabled
            if (config.isIncludeMetadata()) {
                String metadata = extractDocumentMetadata(document);
                markdown = metadata + "\n\n" + markdown;
            }

            document.close();

            return new ConversionResult(
                ConversionResult.Status.SUCCESS,
                inputFile.getPath(),
                outputPath.toString(),
                markdown
            );
        } catch (IOException e) {
            throw new ConversionException("Failed to convert PDF to single file: " + e.getMessage(), e);
        }
    }

    /**
     * Extract document metadata
     */
    private String extractDocumentMetadata(PDDocument document) {
        StringBuilder metadata = new StringBuilder();
        metadata.append("<!-- PDF Document Metadata -->\n");

        try {
            PDDocumentInformation info = document.getDocumentInformation();
            if (info != null) {
                if (info.getTitle() != null) {
                    metadata.append("<!-- Title: ").append(info.getTitle()).append(" -->\n");
                }
                if (info.getAuthor() != null) {
                    metadata.append("<!-- Author: ").append(info.getAuthor()).append(" -->\n");
                }
                if (info.getSubject() != null) {
                    metadata.append("<!-- Subject: ").append(info.getSubject()).append(" -->\n");
                }
                if (info.getCreationDate() != null) {
                    metadata.append("<!-- Created: ").append(info.getCreationDate()).append(" -->\n");
                }
            }

            metadata.append("<!-- Total Pages: ").append(document.getNumberOfPages()).append(" -->\n");

        } catch (Exception e) {
            logger.warning("Failed to extract document metadata: " + e.getMessage());
        }

        return metadata.toString();
    }

    /**
     * Enhanced PDF text stripper with improved structure preservation and element detection
     */
    private static class EnhancedMarkdownPdfTextStripper extends PDFTextStripper {
        private final StringBuilder markdown = new StringBuilder();
        private final List<TextLine> pageLines = new ArrayList<>();
        private final Map<Float, Integer> fontSizeToHeadingLevel = new ConcurrentHashMap<>();
        private final Set<String> detectedImages = new HashSet<>();

        // State for the current line being built
        private StringBuilder currentLineText = new StringBuilder();
        private float currentLineY = -1;
        private float currentLineFontSize = -1;
        private boolean currentLineIsBold = false;
        private boolean currentLineIsItalic = false;
        private String currentFontName = "";

        // Enhanced regex patterns for better structure detection
        private static final Pattern TABLE_LINE_PATTERN = Pattern.compile(".*\\s{3,}.*");
        private static final Pattern LIST_ITEM_PATTERN = Pattern.compile("^(\\d+\\.|[*\\-•])\\s+.*");
        private static final Pattern NUMERIC_LIST_PATTERN = Pattern.compile("^\\d+\\.\\s+");
        private static final Pattern BULLET_LIST_PATTERN = Pattern.compile("^[*\\-•]\\s+");
        private static final Pattern HEADING_PATTERN = Pattern.compile("^[A-Z][A-Z\\s]{2,}$");
        private static final Pattern URL_PATTERN = Pattern.compile("https?://[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=]+");

        public EnhancedMarkdownPdfTextStripper() throws IOException {
            super();
            setSortByPosition(true);
        }

        public EnhancedMarkdownPdfTextStripper(int startPage, int endPage) throws IOException {
            super();
            setSortByPosition(true);
            setStartPage(startPage);
            setEndPage(endPage);
        }

        @Override
        public String getText(PDDocument doc) throws IOException {
            markdown.setLength(0);
            pageLines.clear();
            fontSizeToHeadingLevel.clear();
            detectedImages.clear();

            super.getText(doc);
            return markdown.toString();
        }

        @Override
        protected void writePageStart() throws IOException {
            pageLines.clear();
            resetCurrentLine();
        }

        @Override
        protected void writeString(String text, List<TextPosition> textPositions) throws IOException {
            if (textPositions.isEmpty() || text.trim().isEmpty()) {
                return;
            }

            if (currentLineText.length() == 0) {
                TextPosition firstPosition = textPositions.get(0);
                currentLineY = firstPosition.getY();
                currentLineFontSize = firstPosition.getFontSizeInPt();
                currentLineIsBold = isBold(firstPosition);
                currentLineIsItalic = isItalic(firstPosition);
                currentFontName = firstPosition.getFont().getName();
            }

            currentLineText.append(text);

            // Update style for the whole line analyzing all positions
            for (TextPosition position : textPositions) {
                currentLineFontSize = Math.max(currentLineFontSize, position.getFontSizeInPt());
                if (isBold(position)) {
                    currentLineIsBold = true;
                }
                if (isItalic(position)) {
                    currentLineIsItalic = true;
                }
            }
        }

        @Override
        protected void writeLineSeparator() throws IOException {
            if (currentLineText.length() > 0) {
                pageLines.add(new EnhancedTextLine(
                    currentLineText.toString(),
                    currentLineFontSize,
                    currentLineY,
                    currentLineIsBold,
                    currentLineIsItalic,
                    currentFontName
                ));
            }
            resetCurrentLine();
        }

        @Override
        protected void writePageEnd() throws IOException {
            writeLineSeparator();
            processPageLines();
        }

        private void resetCurrentLine() {
            currentLineText.setLength(0);
            currentLineY = -1;
            currentLineFontSize = -1;
            currentLineIsBold = false;
            currentLineIsItalic = false;
            currentFontName = "";
        }

        /**
         * Enhanced page processing with improved structure detection
         */
        private void processPageLines() {
            if (pageLines.isEmpty()) return;

            float bodyFontSize = getBodyFontSize();
            buildFontSizeMapping(bodyFontSize);

            for (int i = 0; i < pageLines.size(); i++) {
                EnhancedTextLine currentLine = (EnhancedTextLine) pageLines.get(i);
                String lineText = currentLine.text.trim();

                if (lineText.isEmpty()) continue;

                // Enhanced heading detection
                if (isHeading(currentLine, bodyFontSize)) {
                    int headingLevel = determineHeadingLevel(currentLine.fontSize, bodyFontSize);
                    markdown.append("#".repeat(headingLevel)).append(" ").append(lineText).append("\n\n");
                    continue;
                }

                // URL detection and formatting
                if (URL_PATTERN.matcher(lineText).find()) {
                    lineText = URL_PATTERN.matcher(lineText).replaceAll("[$0]($0)");
                }

                // Enhanced list item detection
                if (isListItem(lineText)) {
                    markdown.append(formatEnhancedListItem(lineText)).append("\n");
                    continue;
                }

                // Enhanced table detection
                if (isTableLine(lineText)) {
                    markdown.append(formatTableLine(lineText)).append("\n");
                    if (i == 0 || !isTableLine(pageLines.get(i - 1).text.trim())) {
                        int columnCount = lineText.split("\\s{3,}", -1).length;
                        markdown.append("|" + " --- |".repeat(columnCount)).append("\n");
                    }
                    continue;
                }

                // Text formatting based on style
                String formattedText = applyTextFormatting(lineText, currentLine);

                // Enhanced paragraph handling
                markdown.append(formattedText);
                if (i + 1 < pageLines.size()) {
                    EnhancedTextLine nextLine = (EnhancedTextLine) pageLines.get(i + 1);
                    double verticalGap = Math.abs(nextLine.y - currentLine.y);

                    if (verticalGap > currentLine.fontSize * 1.8) {
                        markdown.append("\n\n"); // Large gap = new paragraph
                    } else if (verticalGap > currentLine.fontSize * 1.2) {
                        markdown.append("\n"); // Medium gap = line break
                    } else {
                        markdown.append(" "); // Small gap = space
                    }
                } else {
                    markdown.append("\n\n"); // End of page
                }
            }
        }

        /**
         * Build font size to heading level mapping
         */
        private void buildFontSizeMapping(float bodyFontSize) {
            Set<Float> uniqueFontSizes = pageLines.stream()
                .map(line -> ((EnhancedTextLine) line).fontSize)
                .filter(size -> size > bodyFontSize * 1.1)
                .collect(Collectors.toSet());

            List<Float> sortedSizes = uniqueFontSizes.stream()
                .sorted(Collections.reverseOrder())
                .collect(Collectors.toList());

            for (int i = 0; i < Math.min(sortedSizes.size(), 6); i++) {
                fontSizeToHeadingLevel.put(sortedSizes.get(i), i + 1);
            }
        }

        /**
         * Enhanced heading detection
         */
        private boolean isHeading(EnhancedTextLine line, float bodyFontSize) {
            return line.isBold ||
                   line.fontSize > bodyFontSize * 1.15 ||
                   HEADING_PATTERN.matcher(line.text.trim()).matches();
        }

        /**
         * Apply text formatting based on style
         */
        private String applyTextFormatting(String text, EnhancedTextLine line) {
            if (line.isBold && line.isItalic) {
                return "***" + text + "***";
            } else if (line.isBold) {
                return "**" + text + "**";
            } else if (line.isItalic) {
                return "*" + text + "*";
            }
            return text;
        }

        /**
         * Checks if a line appears to be part of a table.
         */
        private boolean isTableLine(String text) {
            return TABLE_LINE_PATTERN.matcher(text).matches();
        }

        /**
         * Formats a table row into Markdown syntax.
         */
        private String formatTableLine(String text) {
            String[] cells = text.split("\\s{2,}");
            StringBuilder sb = new StringBuilder("|");
            for (String cell : cells) {
                sb.append(" ").append(cell.trim()).append(" |");
            }
            return sb.toString();
        }

        /**
         * Checks if a line is a list item (numbered or bulleted).
         */
        private boolean isListItem(String text) {
            return LIST_ITEM_PATTERN.matcher(text).matches();
        }

        /**
         * Enhanced list item formatting with better structure preservation
         */
        private String formatEnhancedListItem(String text) {
            if (NUMERIC_LIST_PATTERN.matcher(text).find()) {
                return text; // Keep numbered lists as-is
            } else if (BULLET_LIST_PATTERN.matcher(text).find()) {
                return text; // Keep bullet lists as-is
            } else {
                return "- " + text; // Convert to bullet list
            }
        }

        /**
         * Legacy method for compatibility
         */
        private String formatListItem(String text) {
            return formatEnhancedListItem(text);
        }

        /**
         * Determines the heading level (1, 2, or 3) based on font size relative to the average.
         */
        private int determineHeadingLevel(float fontSize, float bodySize) {
            if (fontSize > bodySize * 1.4) return 1; // Significantly larger
            if (fontSize > bodySize * 1.2) return 2; // Moderately larger
            return 3; // Slightly larger or bold
        }

        /**
         * Calculates the most common font size for the current page to use as the body font size.
         */
        private float getBodyFontSize() {
            if (pageLines.isEmpty()) return 12f; // Default font size

            Map<Float, Long> counts = pageLines.stream()
                    .collect(Collectors.groupingBy(TextLine::getFontSize, Collectors.counting()));

            long maxCount = counts.values().stream().max(Long::compareTo).orElse(0L);

            return counts.entrySet().stream()
                    .filter(entry -> entry.getValue() == maxCount)
                    .map(Map.Entry::getKey)
                    .min(Float::compareTo)
                    .orElse(12.0f);
        }

        /**
         * Enhanced font style detection
         */
        private boolean isBold(TextPosition text) {
            String fontName = text.getFont().getName().toLowerCase();
            return fontName.contains("bold") || fontName.contains("black") || fontName.contains("heavy");
        }

        /**
         * Check if text is italic
         */
        private boolean isItalic(TextPosition text) {
            String fontName = text.getFont().getName().toLowerCase();
            return fontName.contains("italic") || fontName.contains("oblique");
        }

        /**
         * Enhanced data structure for text with comprehensive style and position information
         */
        private static class EnhancedTextLine extends TextLine {
            final boolean isItalic;
            final String fontName;

            EnhancedTextLine(String text, float fontSize, float y, boolean isBold, boolean isItalic, String fontName) {
                super(text, fontSize, y, isBold);
                this.isItalic = isItalic;
                this.fontName = fontName;
            }
        }

        /**
         * Base data structure to hold text along with its essential style and position information.
         */
        private static class TextLine {
            final String text;
            final float fontSize;
            final float y;
            final boolean isBold;

            TextLine(String text, float fontSize, float y, boolean isBold) {
                this.text = text;
                this.fontSize = fontSize;
                this.y = y;
                this.isBold = isBold;
            }

            public float getFontSize() {
                return fontSize;
            }
        }
    }
}
