package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.Plugin;
import com.talkweb.ai.indexer.core.PluginException;
import com.talkweb.ai.indexer.core.PluginRegistry;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 默认插件注册表实现
 */
public class DefaultPluginRegistry implements PluginRegistry {
    
    private final Map<String, Plugin> plugins = new ConcurrentHashMap<>();
    private final Map<Class<?>, Map<String, Plugin>> pluginsByType = new ConcurrentHashMap<>();
    
    @Override
    public void register(Plugin plugin) throws PluginException {
        Objects.requireNonNull(plugin, "Plugin cannot be null");
        String pluginId = plugin.getMetadata().getId();
        
        if (plugins.containsKey(pluginId)) {
            throw new PluginException("Plugin already registered: " + pluginId);
        }
        
        // 注册到主映射
        plugins.put(pluginId, plugin);
        
        // 注册到类型映射
        registerByType(plugin);
    }
    
    @Override
    public boolean unregister(String pluginId) {
        Plugin plugin = plugins.remove(pluginId);
        if (plugin != null) {
            unregisterByType(plugin);
            return true;
        }
        return false;
    }
    
    @Override
    public Collection<Plugin> getAllPlugins() {
        return Collections.unmodifiableCollection(plugins.values());
    }
    
    @Override
    public Optional<Plugin> getPlugin(String pluginId) {
        return Optional.ofNullable(plugins.get(pluginId));
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public <T extends Plugin> Collection<T> getPluginsByType(Class<T> pluginType) {
        Objects.requireNonNull(pluginType, "Plugin type cannot be null");
        
        return plugins.values().stream()
                .filter(pluginType::isInstance)
                .map(plugin -> (T) plugin)
                .collect(Collectors.toList());
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public <T extends Plugin> Optional<T> getPlugin(Class<T> pluginType, String pluginId) {
        Objects.requireNonNull(pluginType, "Plugin type cannot be null");
        Objects.requireNonNull(pluginId, "Plugin ID cannot be null");
        
        Plugin plugin = plugins.get(pluginId);
        if (plugin != null && pluginType.isInstance(plugin)) {
            return Optional.of((T) plugin);
        }
        return Optional.empty();
    }
    
    @Override
    public boolean hasPlugin(String pluginId) {
        return plugins.containsKey(pluginId);
    }
    
    @Override
    public void clear() {
        plugins.clear();
        pluginsByType.clear();
    }
    
    @Override
    public int size() {
        return plugins.size();
    }
    
    private void registerByType(Plugin plugin) {
        Class<?>[] interfaces = plugin.getClass().getInterfaces();
        for (Class<?> iface : interfaces) {
            if (Plugin.class.isAssignableFrom(iface)) {
                pluginsByType
                    .computeIfAbsent(iface, k -> new ConcurrentHashMap<>())
                    .put(plugin.getMetadata().getId(), plugin);
            }
        }
    }
    
    private void unregisterByType(Plugin plugin) {
        Class<?>[] interfaces = plugin.getClass().getInterfaces();
        for (Class<?> iface : interfaces) {
            if (Plugin.class.isAssignableFrom(iface)) {
                Map<String, Plugin> typedPlugins = pluginsByType.get(iface);
                if (typedPlugins != null) {
                    typedPlugins.remove(plugin.getMetadata().getId());
                    if (typedPlugins.isEmpty()) {
                        pluginsByType.remove(iface);
                    }
                }
            }
        }
    }
}
