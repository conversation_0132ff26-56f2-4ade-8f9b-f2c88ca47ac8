package com.talkweb.ai.indexer.core;

import java.nio.file.Path;
import java.util.Collection;
import java.util.Optional;

/**
 * 插件管理器接口，负责插件的生命周期管理
 */
public interface PluginManager {

    /**
     * 启动插件管理器
     * @throws PluginException 如果启动失败
     */
    void start() throws PluginException;
    
    /**
     * 关闭插件管理器
     */
    void shutdown();
    
    /**
     * 加载所有可用的插件
     * @throws PluginException 如果加载过程中发生错误
     */
    void loadPlugins() throws PluginException;

    /**
     * 初始化所有已加载的插件
     * @throws PluginException 如果初始化过程中发生错误
     */
    void initPlugins() throws PluginException;

    /**
     * 启动所有已初始化的插件
     * @throws PluginException 如果启动过程中发生错误
     */
    void startPlugins() throws PluginException;

    /**
     * 停止所有正在运行的插件
     * @throws PluginException 如果停止过程中发生错误
     */
    void stopPlugins() throws PluginException;

    /**
     * 销毁所有插件并释放资源
     * @throws PluginException 如果销毁过程中发生错误
     */
    void destroyPlugins() throws PluginException;

    /**
     * 获取所有已加载的插件
     * @return 插件集合
     */
    Collection<Plugin> getPlugins();

    /**
     * 根据插件ID获取插件
     * @param pluginId 插件ID
     * @return 插件实例，如果不存在则返回空
     */
    Optional<Plugin> getPlugin(String pluginId);

    /**
     * 根据插件ID和版本获取插件
     * @param pluginId 插件ID
     * @param version 插件版本
     * @return 插件实例，如果不存在则返回空
     */
    Optional<Plugin> getPlugin(String pluginId, String version);

    /**
     * 注册插件
     * @param plugin 插件实例
     * @throws PluginException 如果插件已存在或注册失败
     */
    void registerPlugin(Plugin plugin) throws PluginException;

    /**
     * 取消注册插件
     * @param pluginId 插件ID
     * @return 如果插件存在并成功取消注册则返回true，否则返回false
     * @throws PluginException 如果插件正在运行且无法停止
     */
    boolean unregisterPlugin(String pluginId) throws PluginException;

    /**
     * 检查插件是否已加载
     * @param pluginId 插件ID
     * @return 如果插件已加载则返回true，否则返回false
     */
    boolean isPluginLoaded(String pluginId);

    /**
     * 获取插件状态
     * @param pluginId 插件ID
     * @return 插件状态，如果插件不存在则返回null
     */
    PluginState getPluginState(String pluginId);

    /**
     * Installs a plugin from a given path.
     *
     * @param pluginPath Path to the plugin file (JAR).
     * @param force      If true, overwrites any existing plugin with the same ID.
     * @throws PluginException if installation fails.
     */
    void installPlugin(Path pluginPath, boolean force) throws PluginException;

    /**
     * Uninstalls a plugin.
     *
     * @param pluginId The ID of the plugin to uninstall.
     * @param force    If true, forces uninstallation.
     * @return true if uninstalled successfully, false otherwise.
     * @throws PluginException if uninstallation fails.
     */
    boolean uninstallPlugin(String pluginId, boolean force) throws PluginException;

    /**
     * 启用插件热加载
     * @param directory 监控的插件目录
     * @param pollInterval 检查间隔时间(毫秒)
     * @throws PluginException 如果热加载启用失败
     */
    void enableHotReload(Path directory, long pollInterval) throws PluginException;

    /**
     * 禁用插件热加载
     */
    void disableHotReload();

    /**
     * 检查热加载是否启用
     * @return 如果热加载已启用返回true
     */
    boolean isHotReloadEnabled();

    /**
     * 重新加载所有插件
     */
    void reloadPlugins();
}
