package com.talkweb.ai.indexer.core.impl;

import java.util.Properties;
import java.util.logging.Logger;

/**
 * Builder and factory for PDF conversion configuration
 */
public class PdfConversionConfigBuilder {
    
    private static final Logger logger = Logger.getLogger(PdfConversionConfigBuilder.class.getName());
    
    private boolean splitByPages = true;
    private boolean preserveStructure = true;
    private boolean extractImages = false;
    private boolean handleEncrypted = true;
    private String defaultPassword = "";
    private int maxPages = Integer.MAX_VALUE;
    private boolean includeMetadata = true;
    
    /**
     * Create a new builder with default settings
     */
    public static PdfConversionConfigBuilder newBuilder() {
        return new PdfConversionConfigBuilder();
    }
    
    /**
     * Create a builder from properties
     */
    public static PdfConversionConfigBuilder fromProperties(Properties props) {
        PdfConversionConfigBuilder builder = new PdfConversionConfigBuilder();
        
        if (props.containsKey("pdf.splitByPages")) {
            builder.splitByPages(Boolean.parseBoolean(props.getProperty("pdf.splitByPages")));
        }
        if (props.containsKey("pdf.preserveStructure")) {
            builder.preserveStructure(Boolean.parseBoolean(props.getProperty("pdf.preserveStructure")));
        }
        if (props.containsKey("pdf.extractImages")) {
            builder.extractImages(Boolean.parseBoolean(props.getProperty("pdf.extractImages")));
        }
        if (props.containsKey("pdf.handleEncrypted")) {
            builder.handleEncrypted(Boolean.parseBoolean(props.getProperty("pdf.handleEncrypted")));
        }
        if (props.containsKey("pdf.defaultPassword")) {
            builder.defaultPassword(props.getProperty("pdf.defaultPassword"));
        }
        if (props.containsKey("pdf.maxPages")) {
            try {
                builder.maxPages(Integer.parseInt(props.getProperty("pdf.maxPages")));
            } catch (NumberFormatException e) {
                logger.warning("Invalid maxPages value, using default: " + e.getMessage());
            }
        }
        if (props.containsKey("pdf.includeMetadata")) {
            builder.includeMetadata(Boolean.parseBoolean(props.getProperty("pdf.includeMetadata")));
        }
        
        return builder;
    }
    
    public PdfConversionConfigBuilder splitByPages(boolean splitByPages) {
        this.splitByPages = splitByPages;
        return this;
    }
    
    public PdfConversionConfigBuilder preserveStructure(boolean preserveStructure) {
        this.preserveStructure = preserveStructure;
        return this;
    }
    
    public PdfConversionConfigBuilder extractImages(boolean extractImages) {
        this.extractImages = extractImages;
        return this;
    }
    
    public PdfConversionConfigBuilder handleEncrypted(boolean handleEncrypted) {
        this.handleEncrypted = handleEncrypted;
        return this;
    }
    
    public PdfConversionConfigBuilder defaultPassword(String defaultPassword) {
        this.defaultPassword = defaultPassword;
        return this;
    }
    
    public PdfConversionConfigBuilder maxPages(int maxPages) {
        this.maxPages = Math.max(1, maxPages);
        return this;
    }
    
    public PdfConversionConfigBuilder includeMetadata(boolean includeMetadata) {
        this.includeMetadata = includeMetadata;
        return this;
    }
    
    /**
     * Build the configuration
     */
    public PdfToMarkdownConverter.PdfConversionConfig build() {
        PdfToMarkdownConverter.PdfConversionConfig config = new PdfToMarkdownConverter.PdfConversionConfig();
        config.setSplitByPages(splitByPages);
        config.setPreserveStructure(preserveStructure);
        config.setExtractImages(extractImages);
        config.setHandleEncrypted(handleEncrypted);
        config.setDefaultPassword(defaultPassword);
        config.setMaxPages(maxPages);
        config.setIncludeMetadata(includeMetadata);
        return config;
    }
    
    /**
     * Create a configuration optimized for high-quality conversion
     */
    public static PdfToMarkdownConverter.PdfConversionConfig createHighQualityConfig() {
        return newBuilder()
            .splitByPages(true)
            .preserveStructure(true)
            .extractImages(false)
            .handleEncrypted(true)
            .includeMetadata(true)
            .maxPages(Integer.MAX_VALUE)
            .build();
    }
    
    /**
     * Create a configuration optimized for fast conversion
     */
    public static PdfToMarkdownConverter.PdfConversionConfig createFastConfig() {
        return newBuilder()
            .splitByPages(false)
            .preserveStructure(false)
            .extractImages(false)
            .handleEncrypted(false)
            .includeMetadata(false)
            .maxPages(50)
            .build();
    }
    
    /**
     * Create a configuration for batch processing
     */
    public static PdfToMarkdownConverter.PdfConversionConfig createBatchConfig() {
        return newBuilder()
            .splitByPages(true)
            .preserveStructure(true)
            .extractImages(false)
            .handleEncrypted(true)
            .includeMetadata(true)
            .maxPages(100)
            .build();
    }
}
