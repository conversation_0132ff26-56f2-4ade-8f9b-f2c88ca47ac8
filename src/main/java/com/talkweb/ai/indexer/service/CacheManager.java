package com.talkweb.ai.indexer.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * 缓存管理器，用于缓存文档处理结果，支持增量处理。
 */
@Service
public class CacheManager {
    private static final Logger log = LoggerFactory.getLogger(CacheManager.class);

    private final Map<String, CacheEntry> cache = new ConcurrentHashMap<>();
    private final Duration defaultTtl;
    private final int maxEntries;

    /**
     * 创建一个缓存管理器。
     *
     * @param defaultTtl 默认缓存过期时间
     * @param maxEntries 最大缓存条目数
     */
    public CacheManager(Duration defaultTtl, int maxEntries) {
        this.defaultTtl = defaultTtl;
        this.maxEntries = maxEntries;
        log.info("CacheManager initialized with TTL: {}, max entries: {}", defaultTtl, maxEntries);
    }

    /**
     * 创建一个缓存管理器，使用默认配置。
     */
    public CacheManager() {
        this(Duration.ofHours(1), 1000);
    }

    /**
     * 从缓存中获取值，如果不存在则使用提供的函数计算并缓存结果。
     *
     * @param key          缓存键
     * @param valueLoader  如果缓存中不存在，用于计算值的函数
     * @param <T>          值的类型
     * @return 缓存的值或新计算的值
     */
    public <T> T get(String key, Function<String, T> valueLoader) {
        return get(key, valueLoader, defaultTtl);
    }

    /**
     * 从缓存中获取值，如果不存在则使用提供的函数计算并缓存结果，使用指定的TTL。
     *
     * @param key          缓存键
     * @param valueLoader  如果缓存中不存在，用于计算值的函数
     * @param ttl          缓存过期时间
     * @param <T>          值的类型
     * @return 缓存的值或新计算的值
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Function<String, T> valueLoader, Duration ttl) {
        cleanupIfNeeded();
        
        CacheEntry entry = cache.get(key);
        if (entry != null && !entry.isExpired()) {
            log.debug("Cache hit for key: {}", key);
            return (T) entry.getValue();
        }

        log.debug("Cache miss for key: {}, computing new value", key);
        T value = valueLoader.apply(key);
        put(key, value, ttl);
        return value;
    }

    /**
     * 将值放入缓存。
     *
     * @param key   缓存键
     * @param value 要缓存的值
     * @param <T>   值的类型
     */
    public <T> void put(String key, T value) {
        put(key, value, defaultTtl);
    }

    /**
     * 将值放入缓存，使用指定的TTL。
     *
     * @param key   缓存键
     * @param value 要缓存的值
     * @param ttl   缓存过期时间
     * @param <T>   值的类型
     */
    public <T> void put(String key, T value, Duration ttl) {
        cleanupIfNeeded();
        cache.put(key, new CacheEntry(value, ttl));
        log.debug("Added to cache: key={}, ttl={}", key, ttl);
    }

    /**
     * 从缓存中获取值。
     *
     * @param key 缓存键
     * @param <T> 值的类型
     * @return 包含缓存值的Optional，如果不存在或已过期则为空
     */
    @SuppressWarnings("unchecked")
    public <T> Optional<T> get(String key) {
        CacheEntry entry = cache.get(key);
        if (entry != null && !entry.isExpired()) {
            return Optional.of((T) entry.getValue());
        }
        return Optional.empty();
    }

    /**
     * 从缓存中移除值。
     *
     * @param key 缓存键
     * @return 如果值存在并被移除则为true，否则为false
     */
    public boolean remove(String key) {
        CacheEntry removed = cache.remove(key);
        if (removed != null) {
            log.debug("Removed from cache: key={}", key);
            return true;
        }
        return false;
    }

    /**
     * 清空缓存。
     */
    public void clear() {
        cache.clear();
        log.debug("Cache cleared");
    }

    /**
     * 获取缓存大小。
     *
     * @return 缓存中的条目数
     */
    public int size() {
        return cache.size();
    }

    /**
     * 如果需要，清理过期的缓存条目。
     */
    private void cleanupIfNeeded() {
        if (cache.size() >= maxEntries) {
            log.debug("Cache size ({}) reached max entries ({}), cleaning up expired entries", cache.size(), maxEntries);
            cleanup();
        }
    }

    /**
     * 清理过期的缓存条目。
     */
    public void cleanup() {
        int beforeSize = cache.size();
        cache.entrySet().removeIf(entry -> entry.getValue().isExpired());
        int removedCount = beforeSize - cache.size();
        if (removedCount > 0) {
            log.debug("Cleaned up {} expired cache entries", removedCount);
        }
    }

    /**
     * 缓存条目，包含值和过期时间。
     */
    private static class CacheEntry {
        private final Object value;
        private final Instant expirationTime;

        public CacheEntry(Object value, Duration ttl) {
            this.value = value;
            this.expirationTime = Instant.now().plus(ttl);
        }

        public Object getValue() {
            return value;
        }

        public boolean isExpired() {
            return Instant.now().isAfter(expirationTime);
        }
    }
}