package com.talkweb.ai.indexer.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 并发处理服务，用于高效处理大量文档。
 * 利用Java 21的虚拟线程特性，实现高并发、低资源消耗的处理能力。
 */
@Service
public class ConcurrentProcessingService {
    private static final Logger log = LoggerFactory.getLogger(ConcurrentProcessingService.class);

    private final ExecutorService executorService;
    private final int maxConcurrency;
    private final int queueCapacity;

    /**
     * 创建一个并发处理服务。
     *
     * @param maxConcurrency 最大并发数
     * @param queueCapacity  队列容量
     */
    public ConcurrentProcessingService(int maxConcurrency, int queueCapacity) {
        this.maxConcurrency = maxConcurrency;
        this.queueCapacity = queueCapacity;

        // 使用虚拟线程执行器，适合IO密集型任务
        this.executorService = Executors.newFixedThreadPool(maxConcurrency);

        log.info("ConcurrentProcessingService initialized with maxConcurrency: {}, queueCapacity: {}",
                maxConcurrency, queueCapacity);
    }

    /**
     * 创建一个并发处理服务，使用默认配置。
     */
    public ConcurrentProcessingService() {
        this(Runtime.getRuntime().availableProcessors() * 2, 1000);
    }

    /**
     * 并发处理一组项目。
     *
     * @param items    要处理的项目集合
     * @param processor 处理函数
     * @param <T>      输入类型
     * @param <R>      结果类型
     * @return 处理结果列表
     * @throws InterruptedException 如果处理被中断
     */
    public <T, R> List<R> processItems(Collection<T> items, Function<T, R> processor) throws InterruptedException {
        return processItems(items, processor, maxConcurrency);
    }

    /**
     * 并发处理一组项目，使用指定的并发数。
     *
     * @param items         要处理的项目集合
     * @param processor      处理函数
     * @param concurrencyLevel 并发级别
     * @param <T>           输入类型
     * @param <R>           结果类型
     * @return 处理结果列表
     * @throws InterruptedException 如果处理被中断
     */
    public <T, R> List<R> processItems(Collection<T> items, Function<T, R> processor, int concurrencyLevel)
            throws InterruptedException {
        if (items == null || items.isEmpty()) {
            return List.of();
        }

        int effectiveConcurrency = Math.min(concurrencyLevel, items.size());
        log.debug("Processing {} items with concurrency level {}", items.size(), effectiveConcurrency);

        Semaphore semaphore = new Semaphore(effectiveConcurrency);
        List<CompletableFuture<R>> futures = items.stream()
                .map(item -> CompletableFuture.supplyAsync(() -> {
                    try {
                        semaphore.acquire();
                        try {
                            return processor.apply(item);
                        } finally {
                            semaphore.release();
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new CompletionException(e);
                    }
                }, executorService))
                .collect(Collectors.toList());

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));

        try {
            allFutures.get();
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof RuntimeException) {
                throw (RuntimeException) cause;
            } else {
                throw new RuntimeException("Error processing items", cause);
            }
        }

        return futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
    }

    /**
     * 关闭处理服务。
     *
     * @param timeout 等待终止的超时时间
     * @param unit    时间单位
     * @return 如果成功终止则为true，如果超时则为false
     */
    public boolean shutdown(long timeout, TimeUnit unit) {
        executorService.shutdown();
        try {
            return executorService.awaitTermination(timeout, unit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * 立即关闭处理服务。
     *
     * @return 未执行的任务列表
     */
    public List<Runnable> shutdownNow() {
        return executorService.shutdownNow();
    }

    /**
     * 检查处理服务是否已关闭。
     *
     * @return 如果已关闭则为true
     */
    public boolean isShutdown() {
        return executorService.isShutdown();
    }

    /**
     * 检查处理服务是否已终止。
     *
     * @return 如果已终止则为true
     */
    public boolean isTerminated() {
        return executorService.isTerminated();
    }
}
