package com.talkweb.ai.indexer.service;

import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service for scanning files in the filesystem.
 */
@Service
public class FileScannerService {

    /**
     * Scans a directory for files matching the given criteria.
     *
     * @param startPath       The directory to start scanning from.
     * @param recursive       Whether to scan recursively.
     * @param includePatterns An array of wildcard patterns to include.
     * @param excludePatterns An array of wildcard patterns to exclude.
     * @return A list of paths to the matched files.
     * @throws IOException If an I/O error occurs.
     */
    public List<Path> scan(Path startPath, boolean recursive, String[] includePatterns, String[] excludePatterns) throws IOException {
        if (!Files.isDirectory(startPath)) {
            throw new IllegalArgumentException("Start path must be a directory: " + startPath);
        }

        try (Stream<Path> paths = Files.walk(startPath, recursive ? Integer.MAX_VALUE : 1)) {
            return paths
                    .filter(Files::isRegularFile)
                    .filter(path -> shouldProcessFile(path, includePatterns, excludePatterns))
                    .collect(Collectors.toList());
        }
    }

    public boolean shouldProcessFile(Path file, String[] includePatterns, String[] excludePatterns) {
        String fileName = file.getFileName().toString();

        // Check include patterns
        if (includePatterns != null && includePatterns.length > 0) {
            boolean matched = false;
            for (String pattern : includePatterns) {
                if (FilenameUtils.wildcardMatch(fileName, pattern)) {
                    matched = true;
                    break;
                }
            }
            if (!matched) return false;
        }

        // Check exclude patterns
        if (excludePatterns != null && excludePatterns.length > 0) {
            for (String pattern : excludePatterns) {
                if (FilenameUtils.wildcardMatch(fileName, pattern)) {
                    return false;
                }
            }
        }

        return true;
    }
}
