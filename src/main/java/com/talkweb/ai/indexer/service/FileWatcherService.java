package com.talkweb.ai.indexer.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * A service that watches a directory for file changes using a polling mechanism.
 */
public class FileWatcherService {
    private static final Logger log = LoggerFactory.getLogger(FileWatcherService.class);

    private final Path watchPath;
    private final ExecutorService executorService;
    private volatile boolean running = false;
    private final CountDownLatch startLatch = new CountDownLatch(1);
    private Map<Path, Long> lastKnownFiles = new HashMap<>();
    private final long pollInterval = 500;

    private final List<Consumer<Path>> fileCreatedListeners = new ArrayList<>();
    private final List<Consumer<Path>> fileModifiedListeners = new ArrayList<>();
    private final List<Consumer<Path>> fileDeletedListeners = new ArrayList<>();

    public FileWatcherService(Path watchPath) throws IOException {
        this.watchPath = watchPath;
        if (!Files.isDirectory(watchPath)) {
            throw new IllegalArgumentException("Path must be a directory: " + watchPath);
        }
        this.executorService = Executors.newSingleThreadExecutor(r -> {
            Thread t = new Thread(r, "file-watcher-thread");
            t.setDaemon(true);
            return t;
        });
    }

    public void addFileCreatedListener(Consumer<Path> listener) {
        fileCreatedListeners.add(listener);
    }

    public void addFileModifiedListener(Consumer<Path> listener) {
        fileModifiedListeners.add(listener);
    }

    public void addFileDeletedListener(Consumer<Path> listener) {
        fileDeletedListeners.add(listener);
    }

    public void start() {
        if (running) {
            return;
        }
        running = true;
        // Initialize the baseline state
        try {
            this.lastKnownFiles = getCurrentFileState();
        } catch (IOException e) {
            log.error("Failed to establish initial file state for watcher.", e);
            running = false;
            return;
        }

        executorService.submit(() -> {
            startLatch.countDown();
            processEvents();
        });
        log.info("File watcher started for path: {}", watchPath);
    }

    /**
     * For testing purposes. Waits until the watcher thread has started.
     */
    boolean awaitStarted(long timeout, TimeUnit unit) throws InterruptedException {
        return startLatch.await(timeout, unit);
    }

    public void stop() {
        if (!running) {
            return;
        }
        running = false;
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                log.warn("File watcher executor did not terminate in time.");
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            log.warn("Interrupted while waiting for file watcher executor to stop.");
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("File watcher stopped for path: {}", watchPath);
    }

    private void processEvents() {
        while (running) {
            try {
                Map<Path, Long> currentState = getCurrentFileState();
                detectAndNotifyChanges(lastKnownFiles, currentState);
                lastKnownFiles = currentState;

                Thread.sleep(pollInterval);
            } catch (IOException e) {
                log.error("Error during file polling.", e);
            } catch (InterruptedException e) {
                if (!running) {
                    log.info("File watcher polling interrupted. Stopping.");
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
    }

    private void detectAndNotifyChanges(Map<Path, Long> previousState, Map<Path, Long> currentState) {
        // Detect creations and modifications
        for (Map.Entry<Path, Long> entry : currentState.entrySet()) {
            Path path = entry.getKey();
            Long currentTimestamp = entry.getValue();

            if (!previousState.containsKey(path)) {
                notifyListeners(fileCreatedListeners, path);
            } else {
                Long previousTimestamp = previousState.get(path);
                if (!currentTimestamp.equals(previousTimestamp)) {
                    notifyListeners(fileModifiedListeners, path);
                }
            }
        }

        // Detect deletions
        for (Path path : previousState.keySet()) {
            if (!currentState.containsKey(path)) {
                notifyListeners(fileDeletedListeners, path);
            }
        }
    }

    private Map<Path, Long> getCurrentFileState() throws IOException {
        if (!Files.exists(watchPath)) {
            return new HashMap<>();
        }
        try (var stream = Files.list(watchPath)) {
            return stream
                .filter(Files::isRegularFile)
                .collect(Collectors.toMap(path -> path, path -> {
                    try {
                        return Files.getLastModifiedTime(path).toMillis();
                    } catch (IOException e) {
                        return -1L;
                    }
                }));
        }
    }

    private void notifyListeners(List<Consumer<Path>> listeners, Path path) {
        for (Consumer<Path> listener : listeners) {
            try {
                listener.accept(path);
            } catch (Exception e) {
                log.error("Error in file watcher listener for path {}:", path, e);
            }
        }
    }
}
