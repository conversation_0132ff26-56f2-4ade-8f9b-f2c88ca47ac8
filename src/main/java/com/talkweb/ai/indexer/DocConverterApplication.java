package com.talkweb.ai.indexer;

import com.talkweb.ai.indexer.cli.DocConverterCommand;
import com.talkweb.ai.indexer.cli.util.ConsoleColors;
import com.talkweb.ai.indexer.cli.util.ConsoleLogger;
import com.talkweb.ai.indexer.core.PluginException;
import com.talkweb.ai.indexer.core.PluginManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import picocli.CommandLine;

@SpringBootApplication
public class DocConverterApplication implements CommandLineRunner {

    private final DocConverterCommand docConverterCommand;
    private final CommandLine.IFactory factory;
    private final PluginManager pluginManager;
    private final ConsoleLogger logger = new ConsoleLogger();

    public DocConverterApplication(DocConverterCommand docConverterCommand,
                                  @Qualifier("commandLineFactory") CommandLine.IFactory factory,
                                  PluginManager pluginManager) {
        this.docConverterCommand = docConverterCommand;
        this.factory = factory;
        this.pluginManager = pluginManager;
    }

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(DocConverterApplication.class);
        app.setBannerMode(org.springframework.boot.Banner.Mode.OFF);
        System.exit(SpringApplication.exit(app.run(args)));
    }


    @Override
    public void run(String... args) {
        int exitCode = 0;
        try {
            logger.info("Starting plugin manager...");
            pluginManager.start();
            logger.success("Plugin manager started successfully.");

            CommandLine commandLine = new CommandLine(docConverterCommand, factory)
                .setExecutionStrategy(new CommandLine.RunLast())
                .setColorScheme(CommandLine.Help.defaultColorScheme(CommandLine.Help.Ansi.AUTO));

            commandLine.setExecutionExceptionHandler((ex, cmd, parseResult) -> {
                logger.error("Error executing command: %s", ex.getMessage());
                if (parseResult.hasMatchedOption("verbose")) {
                    ex.printStackTrace();
                }
                return 1;
            });

            // If no arguments are provided, show help and exit
            if (args.length == 0) {
                commandLine.usage(System.out);
                exitCode = 0;
            } else {
                exitCode = commandLine.execute(args);
            }

        } catch (PluginException e) {
            logger.error("Failed to start plugin manager: %s", e.getMessage());
            exitCode = 1;
        } catch (Exception e) {
            logger.error("An unexpected error occurred: %s", e.getMessage());
            exitCode = 1;
        } finally {
            shutdownPluginManager();
        }

        // The exit code will be handled by SpringApplication.exit
    }

    private void shutdownPluginManager() {
        try {
            logger.info("Shutting down plugin manager...");
            pluginManager.shutdown();
            logger.success("Plugin manager shut down successfully.");
        } catch (Exception e) {
            logger.error("Error during plugin manager shutdown: %s", e.getMessage());
        }
    }
}
