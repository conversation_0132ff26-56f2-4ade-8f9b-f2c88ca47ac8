package com.talkweb.ai.indexer.util.markdown.converter;

import com.talkweb.ai.indexer.util.markdown.ConversionContext;
import com.talkweb.ai.indexer.util.markdown.MarkdownBuilder;
import org.jsoup.nodes.Element;

import java.util.Set;

import static com.talkweb.ai.indexer.util.markdown.MarkdownConstants.*;

/**
 * Converter for HTML block-level elements (p, div, blockquote, hr, br)
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class BlockElementConverter extends AbstractElementConverter {
    
    private static final Set<String> SUPPORTED_TAGS = Set.of(
        TAG_P, TAG_DIV, TAG_BLOCKQUOTE, TAG_HR, TAG_BR,
        TAG_SECTION, TAG_ARTICLE, TAG_HEADER, TAG_FOOTER,
        TAG_MAIN, TAG_ASIDE, TAG_NAV, TAG_SPAN, "body"
    );
    
    @Override
    public Set<String> getSupportedTags() {
        return SUPPORTED_TAGS;
    }
    
    @Override
    public void convert(Element element, MarkdownBuilder builder, ConversionContext context) 
            throws ConversionException {
        validate(element, context);
        
        String tagName = element.tagName().toLowerCase();
        
        switch (tagName) {
            case TAG_P:
                convertParagraph(element, builder, context);
                break;
            case TAG_BLOCKQUOTE:
                convertBlockquote(element, builder, context);
                break;
            case TAG_HR:
                convertHorizontalRule(element, builder, context);
                break;
            case TAG_BR:
                convertLineBreak(element, builder, context);
                break;
            case TAG_DIV:
            case TAG_SECTION:
            case TAG_ARTICLE:
            case TAG_HEADER:
            case TAG_FOOTER:
            case TAG_MAIN:
            case TAG_ASIDE:
            case TAG_NAV:
            case "body":
                convertContainer(element, builder, context);
                break;
            case TAG_SPAN:
                convertInlineContainer(element, builder, context);
                break;
            default:
                throw new ConversionException("Unsupported block element: " + tagName);
        }
    }
    
    @Override
    public int getPriority() {
        return 50; // Medium priority for block elements
    }
    
    /**
     * Converts paragraph elements
     *
     * @param element the p element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    private void convertParagraph(Element element, MarkdownBuilder builder, ConversionContext context) {
        if (!builder.isEmpty()) {
            builder.newline();
        }
        processChildren(element, builder, context);
        builder.newline();
    }
    
    /**
     * Converts blockquote elements
     * 
     * @param element the blockquote element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    private void convertBlockquote(Element element, MarkdownBuilder builder, ConversionContext context) {
        builder.newline();
        
        // Extract content and split into lines
        MarkdownBuilder contentBuilder = new MarkdownBuilder();
        processChildren(element, contentBuilder, context);
        String content = contentBuilder.toString().trim();
        
        if (!content.isEmpty()) {
            String[] lines = content.split(NEWLINE);
            for (String line : lines) {
                line = line.trim();
                if (!line.isEmpty()) {
                    builder.blockquote(line);
                }
            }
        }
        
        builder.newline();
    }
    
    /**
     * Converts horizontal rule elements
     * 
     * @param element the hr element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    private void convertHorizontalRule(Element element, MarkdownBuilder builder, ConversionContext context) {
        builder.horizontalRule();
    }
    
    /**
     * Converts line break elements
     * 
     * @param element the br element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    private void convertLineBreak(Element element, MarkdownBuilder builder, ConversionContext context) {
        builder.lineBreak();
    }
    
    /**
     * Converts container elements (div, section, etc.)
     * 
     * @param element the container element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    private void convertContainer(Element element, MarkdownBuilder builder, ConversionContext context) {
        // For container elements, just process children
        // Add spacing if this is a top-level container
        boolean isTopLevel = !context.hasAncestor(TAG_DIV) && 
                           !context.hasAncestor(TAG_SECTION) &&
                           !context.hasAncestor(TAG_ARTICLE);
        
        if (isTopLevel && !builder.isEmpty()) {
            builder.newline();
        }
        
        processChildren(element, builder, context);
        
        if (isTopLevel) {
            builder.newline();
        }
    }
    
    /**
     * Converts inline container elements (span)
     * 
     * @param element the span element
     * @param builder the markdown builder
     * @param context the conversion context
     */
    private void convertInlineContainer(Element element, MarkdownBuilder builder, ConversionContext context) {
        // For inline containers, just process children without adding spacing
        processChildren(element, builder, context);
    }
    
    @Override
    public boolean shouldProcessChildren(Element element, ConversionContext context) {
        String tagName = element.tagName().toLowerCase();
        // All block elements should process children, except hr and br
        return !TAG_HR.equals(tagName) && !TAG_BR.equals(tagName);
    }
}
