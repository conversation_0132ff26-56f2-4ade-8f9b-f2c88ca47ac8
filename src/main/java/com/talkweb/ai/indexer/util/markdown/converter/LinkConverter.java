package com.talkweb.ai.indexer.util.markdown.converter;

import com.talkweb.ai.indexer.util.markdown.ConversionContext;
import com.talkweb.ai.indexer.util.markdown.MarkdownBuilder;
import org.jsoup.nodes.Element;

import java.util.Set;

import static com.talkweb.ai.indexer.util.markdown.MarkdownConstants.*;

/**
 * Converter for HTML link elements (a)
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class LinkConverter extends AbstractElementConverter {
    
    private static final Set<String> SUPPORTED_TAGS = Set.of(TAG_A);
    
    @Override
    public Set<String> getSupportedTags() {
        return SUPPORTED_TAGS;
    }
    
    @Override
    public void convert(Element element, MarkdownBuilder builder, ConversionContext context) 
            throws ConversionException {
        validate(element, context);
        
        String href = getAttribute(element, ATTR_HREF);
        String title = getAttribute(element, ATTR_TITLE);
        
        // If no href, just process children as plain text
        if (href.isEmpty()) {
            processChildren(element, builder, context);
            return;
        }
        
        // Get link text from element content
        String linkText = extractTextContent(element);

        // If no link text, use the href as text
        if (linkText.isEmpty()) {
            linkText = href;
        }
        
        // Create the markdown link
        if (title.isEmpty()) {
            builder.link(linkText, href);
        } else {
            builder.link(linkText, href, title);
        }
    }
    
    @Override
    public boolean shouldProcessChildren(Element element, ConversionContext context) {
        // We process children manually to build link text
        return false;
    }
    
    @Override
    public int getPriority() {
        return 70; // Medium-high priority for links
    }
    
    @Override
    public void validate(Element element, ConversionContext context) throws ConversionException {
        super.validate(element, context);
        
        // Additional validation for links
        if (context.isStrictMode()) {
            String href = getAttribute(element, ATTR_HREF);
            if (!href.isEmpty() && !isValidUrl(href)) {
                throw new ConversionException("Invalid URL in strict mode: " + href);
            }
        }
    }
    
    /**
     * Validates if a URL is well-formed (basic validation)
     * 
     * @param url the URL to validate
     * @return true if the URL appears valid
     */
    private boolean isValidUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        // Basic URL validation - starts with protocol or is relative
        url = url.trim();
        return url.startsWith("http://") || 
               url.startsWith("https://") || 
               url.startsWith("ftp://") || 
               url.startsWith("mailto:") || 
               url.startsWith("/") || 
               url.startsWith("./") || 
               url.startsWith("../") ||
               url.startsWith("#");
    }
}
