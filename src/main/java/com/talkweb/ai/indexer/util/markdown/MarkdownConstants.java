package com.talkweb.ai.indexer.util.markdown;

/**
 * Constants used in HTML to Markdown conversion
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public final class MarkdownConstants {
    
    // Prevent instantiation
    private MarkdownConstants() {
        throw new UnsupportedOperationException("Utility class");
    }
    
    // Markdown syntax constants
    public static final String HEADING_PREFIX = "#";
    public static final String BOLD_MARKER = "**";
    public static final String ITALIC_MARKER = "*";
    public static final String CODE_MARKER = "`";
    public static final String CODE_BLOCK_MARKER = "```";
    public static final String BLOCKQUOTE_PREFIX = "> ";
    public static final String UNORDERED_LIST_MARKER = "- ";
    public static final String HORIZONTAL_RULE = "---";
    public static final String LINE_BREAK = "  \n";
    public static final String TABLE_SEPARATOR = "|";
    public static final String TABLE_HEADER_SEPARATOR = "---|";
    
    // Whitespace and formatting
    public static final String NEWLINE = "\n";
    public static final String DOUBLE_NEWLINE = "\n\n";
    public static final String SPACE = " ";
    public static final String EMPTY_STRING = "";
    
    // HTML entities
    public static final String ENTITY_LT = "&lt;";
    public static final String ENTITY_GT = "&gt;";
    public static final String ENTITY_AMP = "&amp;";
    public static final String ENTITY_QUOT = "&quot;";
    public static final String ENTITY_APOS = "&apos;";
    public static final String ENTITY_NBSP = "&nbsp;";
    public static final String ENTITY_COPY = "&copy;";
    public static final String ENTITY_REG = "&reg;";
    public static final String ENTITY_TRADE = "&trade;";
    public static final String ENTITY_HELLIP = "&hellip;";
    public static final String ENTITY_MDASH = "&mdash;";
    public static final String ENTITY_NDASH = "&ndash;";
    public static final String ENTITY_LSQUO = "&lsquo;";
    public static final String ENTITY_RSQUO = "&rsquo;";
    public static final String ENTITY_LDQUO = "&ldquo;";
    public static final String ENTITY_RDQUO = "&rdquo;";
    
    // Decoded entities
    public static final String DECODED_LT = "<";
    public static final String DECODED_GT = ">";
    public static final String DECODED_AMP = "&";
    public static final String DECODED_QUOT = "\"";
    public static final String DECODED_APOS = "'";
    public static final String DECODED_NBSP = " ";
    public static final String DECODED_COPY = "©";
    public static final String DECODED_REG = "®";
    public static final String DECODED_TRADE = "™";
    public static final String DECODED_HELLIP = "…";
    public static final String DECODED_MDASH = "—";
    public static final String DECODED_NDASH = "–";
    public static final String DECODED_LSQUO = "'";
    public static final String DECODED_RSQUO = "'";
    public static final String DECODED_LDQUO = "\"";
    public static final String DECODED_RDQUO = "\"";
    
    // HTML tag names
    public static final String TAG_H1 = "h1";
    public static final String TAG_H2 = "h2";
    public static final String TAG_H3 = "h3";
    public static final String TAG_H4 = "h4";
    public static final String TAG_H5 = "h5";
    public static final String TAG_H6 = "h6";
    public static final String TAG_P = "p";
    public static final String TAG_A = "a";
    public static final String TAG_IMG = "img";
    public static final String TAG_STRONG = "strong";
    public static final String TAG_B = "b";
    public static final String TAG_EM = "em";
    public static final String TAG_I = "i";
    public static final String TAG_CODE = "code";
    public static final String TAG_PRE = "pre";
    public static final String TAG_BLOCKQUOTE = "blockquote";
    public static final String TAG_UL = "ul";
    public static final String TAG_OL = "ol";
    public static final String TAG_LI = "li";
    public static final String TAG_TABLE = "table";
    public static final String TAG_TR = "tr";
    public static final String TAG_TD = "td";
    public static final String TAG_TH = "th";
    public static final String TAG_THEAD = "thead";
    public static final String TAG_TBODY = "tbody";
    public static final String TAG_TFOOT = "tfoot";
    public static final String TAG_HR = "hr";
    public static final String TAG_BR = "br";
    public static final String TAG_DIV = "div";
    public static final String TAG_SPAN = "span";
    public static final String TAG_SECTION = "section";
    public static final String TAG_ARTICLE = "article";
    public static final String TAG_HEADER = "header";
    public static final String TAG_FOOTER = "footer";
    public static final String TAG_MAIN = "main";
    public static final String TAG_ASIDE = "aside";
    public static final String TAG_NAV = "nav";
    public static final String TAG_FORM = "form";
    public static final String TAG_INPUT = "input";
    public static final String TAG_SELECT = "select";
    public static final String TAG_TEXTAREA = "textarea";
    public static final String TAG_BUTTON = "button";
    public static final String TAG_LABEL = "label";
    public static final String TAG_SCRIPT = "script";
    public static final String TAG_STYLE = "style";
    
    // HTML attributes
    public static final String ATTR_HREF = "href";
    public static final String ATTR_SRC = "src";
    public static final String ATTR_ALT = "alt";
    public static final String ATTR_TITLE = "title";
    
    // Configuration defaults
    public static final int DEFAULT_INITIAL_CAPACITY = 1024;
    public static final int DEFAULT_MAX_CACHE_SIZE = 1000;
    public static final long DEFAULT_CACHE_TTL_MINUTES = 60;
    
    // Regular expression patterns (will be compiled once)
    public static final String WHITESPACE_PATTERN = "\\s+";
    public static final String EXCESSIVE_NEWLINES_PATTERN = "\n{3,}";
    public static final String TRAILING_WHITESPACE_PATTERN = "\\s+$";
    public static final String BLOCK_ELEMENT_PATTERN = "^(div|p|h[1-6]|ul|ol|li|table|tr|td|th|thead|tbody|tfoot|blockquote|pre|hr|section|article|header|footer|main|aside|nav)$";
    
    // Error messages
    public static final String ERROR_NULL_ELEMENT = "Element cannot be null";
    public static final String ERROR_NULL_CONTEXT = "Conversion context cannot be null";
    public static final String ERROR_NULL_BUILDER = "Markdown builder cannot be null";
    public static final String ERROR_UNSUPPORTED_ELEMENT = "Unsupported HTML element: %s";
    public static final String ERROR_CONVERSION_FAILED = "HTML to Markdown conversion failed";
    public static final String ERROR_STRICT_MODE_VIOLATION = "HTML contains unsupported elements in STRICT mode";
}
