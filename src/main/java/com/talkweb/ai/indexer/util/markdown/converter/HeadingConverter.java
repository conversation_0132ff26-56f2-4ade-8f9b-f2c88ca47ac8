package com.talkweb.ai.indexer.util.markdown.converter;

import com.talkweb.ai.indexer.util.markdown.ConversionContext;
import com.talkweb.ai.indexer.util.markdown.MarkdownBuilder;
import org.jsoup.nodes.Element;

import java.util.Set;

import static com.talkweb.ai.indexer.util.markdown.MarkdownConstants.*;

/**
 * Converter for HTML heading elements (h1-h6)
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class HeadingConverter extends AbstractElementConverter {
    
    private static final Set<String> SUPPORTED_TAGS = Set.of(
        TAG_H1, TAG_H2, TAG_H3, TAG_H4, TAG_H5, TAG_H6
    );
    
    @Override
    public Set<String> getSupportedTags() {
        return SUPPORTED_TAGS;
    }
    
    @Override
    public void convert(Element element, MarkdownBuilder builder, ConversionContext context) 
            throws ConversionException {
        validate(element, context);
        
        String tagName = element.tagName().toLowerCase();
        int level = getHeadingLevel(tagName);
        
        if (level < 1 || level > 6) {
            throw new ConversionException("Invalid heading level: " + level);
        }
        
        String text = extractTextContent(element);
        if (!text.isEmpty()) {
            if (!builder.isEmpty()) {
                builder.newline();
            }
            builder.heading(level, text);
        }
    }
    
    @Override
    public boolean shouldProcessChildren(Element element, ConversionContext context) {
        // We extract text content directly, so don't process children separately
        return false;
    }
    
    @Override
    public int getPriority() {
        return 100; // High priority for headings
    }
    
    /**
     * Extracts the heading level from the tag name
     * 
     * @param tagName the HTML tag name (h1, h2, etc.)
     * @return the heading level (1-6)
     */
    private int getHeadingLevel(String tagName) {
        if (tagName == null || tagName.length() != 2 || !tagName.startsWith("h")) {
            return 0;
        }
        
        try {
            return Integer.parseInt(tagName.substring(1));
        } catch (NumberFormatException e) {
            return 0;
        }
    }
}
