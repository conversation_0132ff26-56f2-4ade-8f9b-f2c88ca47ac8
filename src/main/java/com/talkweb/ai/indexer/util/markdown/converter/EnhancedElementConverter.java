package com.talkweb.ai.indexer.util.markdown.converter;

import com.talkweb.ai.indexer.util.markdown.ConversionContext;
import com.talkweb.ai.indexer.util.markdown.MarkdownBuilder;
import org.jsoup.nodes.Element;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Enhanced base converter for HTML elements with framework compatibility
 * 
 * Provides common functionality for framework detection, caching, and component processing
 * that can be used by specific enhanced converters.
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public abstract class EnhancedElementConverter extends AbstractElementConverter {
    
    // Performance optimization: Framework detection cache
    protected static final Map<String, UIFramework> FRAMEWORK_CACHE = new ConcurrentHashMap<>();
    protected static final int MAX_FRAMEWORK_CACHE_SIZE = 1000;
    
    // Performance optimization: Component processing cache
    protected static final Map<String, String> COMPONENT_TEXT_CACHE = new ConcurrentHashMap<>();
    protected static final int MAX_COMPONENT_CACHE_SIZE = 500;
    
    /**
     * Enum representing different UI frameworks
     */
    public enum UIFramework {
        BOOTSTRAP,
        TAILWIND,
        ANT_DESIGN,
        ELEMENT_UI,
        VUETIFY,
        MATERIAL_UI,
        SEMANTIC_UI,
        FOUNDATION,
        BULMA,
        STANDARD
    }
    
    // Framework-specific CSS classes for identification
    protected static final Set<String> BOOTSTRAP_CLASSES = Set.of(
        "btn", "form-control", "form-group", "input-group", "card", "modal", "alert",
        "navbar", "nav", "nav-tabs", "nav-pills", "breadcrumb", "dropdown",
        "table", "table-striped", "table-bordered", "badge", "progress"
    );
    
    protected static final Set<String> TAILWIND_CLASSES = Set.of(
        "bg-", "text-", "border-", "rounded", "shadow", "flex", "grid",
        "p-", "m-", "w-", "h-", "space-", "divide-"
    );
    
    protected static final Set<String> ANT_DESIGN_CLASSES = Set.of(
        "ant-btn", "ant-form", "ant-input", "ant-select", "ant-card", "ant-modal", "ant-alert",
        "ant-menu", "ant-breadcrumb", "ant-dropdown", "ant-table", "ant-tag", "ant-badge",
        "ant-progress", "ant-avatar", "ant-tooltip", "ant-switch"
    );
    
    protected static final Set<String> ELEMENT_UI_CLASSES = Set.of(
        "el-button", "el-form", "el-input", "el-select", "el-card", "el-dialog", "el-alert",
        "el-menu", "el-breadcrumb", "el-dropdown", "el-table", "el-tag", "el-badge",
        "el-progress", "el-avatar", "el-tooltip", "el-switch"
    );
    
    protected static final Set<String> VUETIFY_CLASSES = Set.of(
        "v-btn", "v-form", "v-text-field", "v-select", "v-card", "v-dialog", "v-alert",
        "v-navigation-drawer", "v-breadcrumbs", "v-menu", "v-data-table", "v-chip", "v-badge",
        "v-progress-linear", "v-progress-circular", "v-avatar", "v-tooltip", "v-switch"
    );
    
    protected static final Set<String> MATERIAL_UI_CLASSES = Set.of(
        "MuiButton", "MuiTextField", "MuiSelect", "MuiCard", "MuiDialog", "MuiAlert",
        "MuiAppBar", "MuiBreadcrumbs", "MuiMenu", "MuiTable", "MuiChip", "MuiBadge",
        "MuiLinearProgress", "MuiCircularProgress", "MuiAvatar", "MuiTooltip", "MuiSwitch"
    );
    
    protected static final Set<String> SEMANTIC_UI_CLASSES = Set.of(
        "ui", "button", "form", "input", "dropdown", "card", "modal", "message",
        "menu", "breadcrumb", "table", "label", "progress"
    );
    
    protected static final Set<String> FOUNDATION_CLASSES = Set.of(
        "button", "form", "input-group", "card", "reveal", "callout",
        "menu", "breadcrumbs", "table", "label", "progress"
    );
    
    protected static final Set<String> BULMA_CLASSES = Set.of(
        "button", "field", "input", "select", "card", "modal", "notification",
        "navbar", "breadcrumb", "table", "tag", "progress"
    );
    
    /**
     * Interface for component processing logic
     */
    protected interface ComponentProcessor {
        String process(Element element);
    }
    
    /**
     * Detects the UI framework based on CSS classes and structure with caching
     *
     * @param element the element to analyze
     * @return the detected framework
     */
    protected UIFramework detectUIFramework(Element element) {
        String cacheKey = generateFrameworkCacheKey(element);
        
        // Check cache first
        UIFramework cachedFramework = FRAMEWORK_CACHE.get(cacheKey);
        if (cachedFramework != null) {
            return cachedFramework;
        }
        
        // Perform detection
        UIFramework framework = detectUIFrameworkInternal(element);
        
        // Cache the result (with size limit)
        if (FRAMEWORK_CACHE.size() < MAX_FRAMEWORK_CACHE_SIZE) {
            FRAMEWORK_CACHE.put(cacheKey, framework);
        }
        
        return framework;
    }
    
    /**
     * Generates a cache key for framework detection
     */
    protected String generateFrameworkCacheKey(Element element) {
        StringBuilder keyBuilder = new StringBuilder();
        Element current = element;
        int depth = 0;
        
        // Include classes from element and up to 3 ancestors
        while (current != null && depth < 4) {
            String className = current.className().toLowerCase().trim();
            if (!className.isEmpty()) {
                if (keyBuilder.length() > 0) {
                    keyBuilder.append("|");
                }
                keyBuilder.append(className);
            }
            current = current.parent();
            depth++;
        }
        
        return keyBuilder.toString();
    }
    
    /**
     * Internal framework detection logic
     */
    protected UIFramework detectUIFrameworkInternal(Element element) {
        Element current = element;
        while (current != null) {
            String className = current.className().toLowerCase();
            Set<String> classes = Set.of(className.split("\\s+"));
            
            // Check for framework-specific classes
            if (classes.stream().anyMatch(BOOTSTRAP_CLASSES::contains)) {
                return UIFramework.BOOTSTRAP;
            }
            if (classes.stream().anyMatch(cls -> TAILWIND_CLASSES.stream().anyMatch(cls::startsWith))) {
                return UIFramework.TAILWIND;
            }
            if (classes.stream().anyMatch(ANT_DESIGN_CLASSES::contains)) {
                return UIFramework.ANT_DESIGN;
            }
            if (classes.stream().anyMatch(ELEMENT_UI_CLASSES::contains)) {
                return UIFramework.ELEMENT_UI;
            }
            if (classes.stream().anyMatch(VUETIFY_CLASSES::contains)) {
                return UIFramework.VUETIFY;
            }
            if (classes.stream().anyMatch(MATERIAL_UI_CLASSES::contains)) {
                return UIFramework.MATERIAL_UI;
            }
            if (classes.stream().anyMatch(SEMANTIC_UI_CLASSES::contains)) {
                return UIFramework.SEMANTIC_UI;
            }
            if (classes.stream().anyMatch(FOUNDATION_CLASSES::contains)) {
                return UIFramework.FOUNDATION;
            }
            if (classes.stream().anyMatch(BULMA_CLASSES::contains)) {
                return UIFramework.BULMA;
            }
            
            current = current.parent();
        }
        
        return UIFramework.STANDARD;
    }
    
    /**
     * Memory-optimized component text extraction with caching
     */
    protected String extractComponentTextOptimized(Element element, String componentType) {
        String elementKey = componentType + ":" + element.className() + ":" + element.text();
        
        // Check cache first
        String cachedText = COMPONENT_TEXT_CACHE.get(elementKey);
        if (cachedText != null) {
            return cachedText;
        }
        
        String text = element.text();
        
        // Cache the result (with size limit)
        if (COMPONENT_TEXT_CACHE.size() < MAX_COMPONENT_CACHE_SIZE) {
            COMPONENT_TEXT_CACHE.put(elementKey, text);
        }
        
        return text;
    }
    
    /**
     * Memory-optimized generic component processing
     */
    protected String processComponentsOptimized(Element element, String[] removeSelectors,
                                              Map<String, ComponentProcessor> componentSelectors) {
        StringBuilder result = new StringBuilder();
        processElementRecursively(element, result, removeSelectors, componentSelectors);
        return result.toString().trim();
    }
    
    /**
     * Recursively processes element content without cloning
     */
    protected void processElementRecursively(Element element, StringBuilder result,
                                           String[] removeSelectors, Map<String, ComponentProcessor> componentSelectors) {
        // Skip elements that should be removed
        for (String selector : removeSelectors) {
            if (element.is(selector)) {
                return;
            }
        }
        
        // Check if this element matches any component selectors
        for (Map.Entry<String, ComponentProcessor> entry : componentSelectors.entrySet()) {
            if (element.is(entry.getKey())) {
                String processedText = entry.getValue().process(element);
                if (!processedText.isEmpty()) {
                    result.append(processedText).append(" ");
                }
                return;
            }
        }
        
        // If it's a text node, add the text
        if (element.children().isEmpty()) {
            String text = element.text();
            if (!text.isEmpty()) {
                result.append(text).append(" ");
            }
        } else {
            // Process children
            for (Element child : element.children()) {
                processElementRecursively(child, result, removeSelectors, componentSelectors);
            }
        }
    }
    
    /**
     * Extracts state information from an element (active, disabled, selected, etc.)
     */
    protected Map<String, Boolean> extractElementState(Element element) {
        Map<String, Boolean> state = new HashMap<>();
        
        String className = element.className().toLowerCase();
        Set<String> classes = Set.of(className.split("\\s+"));
        
        // Common state classes across frameworks
        state.put("active", classes.contains("active") || classes.contains("is-active") || 
                           classes.contains("ant-menu-item-selected") || classes.contains("el-menu-item-is-active"));
        state.put("disabled", classes.contains("disabled") || classes.contains("is-disabled") || 
                             element.hasAttr("disabled"));
        state.put("selected", classes.contains("selected") || classes.contains("is-selected") || 
                             element.hasAttr("selected"));
        state.put("checked", classes.contains("checked") || classes.contains("is-checked") || 
                            element.hasAttr("checked"));
        
        return state;
    }
    
    /**
     * Clears all performance caches to free memory
     */
    public static void clearEnhancedCaches() {
        FRAMEWORK_CACHE.clear();
        COMPONENT_TEXT_CACHE.clear();
    }
    
    /**
     * Gets cache statistics for monitoring
     */
    public static Map<String, Integer> getEnhancedCacheStatistics() {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("frameworkCacheSize", FRAMEWORK_CACHE.size());
        stats.put("componentCacheSize", COMPONENT_TEXT_CACHE.size());
        stats.put("frameworkCacheLimit", MAX_FRAMEWORK_CACHE_SIZE);
        stats.put("componentCacheLimit", MAX_COMPONENT_CACHE_SIZE);
        return stats;
    }
}
