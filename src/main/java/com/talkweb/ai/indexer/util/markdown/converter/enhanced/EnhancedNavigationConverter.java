package com.talkweb.ai.indexer.util.markdown.converter.enhanced;

import com.talkweb.ai.indexer.util.markdown.ConversionContext;
import com.talkweb.ai.indexer.util.markdown.MarkdownBuilder;
import com.talkweb.ai.indexer.util.markdown.converter.EnhancedElementConverter;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.*;

import static com.talkweb.ai.indexer.util.markdown.MarkdownConstants.*;

/**
 * Enhanced converter for HTML navigation elements with framework compatibility
 * 
 * Supports:
 * - Bootstrap navigation (navbar, nav-tabs, nav-pills, breadcrumb, etc.)
 * - Ant Design navigation (ant-menu, ant-breadcrumb, ant-dropdown, etc.)
 * - Element UI navigation (el-menu, el-breadcrumb, el-dropdown, etc.)
 * - Vuetify navigation (v-navigation-drawer, v-breadcrumbs, v-menu, etc.)
 * - Material-UI navigation (MuiAppBar, MuiBreadcrumbs, MuiMenu, etc.)
 * - Semantic UI navigation (ui menu, breadcrumb, etc.)
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class EnhancedNavigationConverter extends EnhancedElementConverter {
    
    private static final Set<String> SUPPORTED_TAGS = Set.of(
        TAG_NAV, TAG_UL, TAG_OL, TAG_LI, "menu"
    );
    
    // Navigation-specific CSS classes for identification
    private static final Set<String> NAVIGATION_CLASSES = Set.of(
        "nav", "navbar", "nav-tabs", "nav-pills", "breadcrumb", "menu", "navigation",
        "ant-menu", "ant-breadcrumb", "ant-dropdown-menu",
        "el-menu", "el-breadcrumb", "el-dropdown-menu",
        "v-navigation-drawer", "v-breadcrumbs", "v-menu",
        "MuiAppBar", "MuiBreadcrumbs", "MuiMenu", "MuiTabs",
        "ui menu", "ui breadcrumb"
    );
    
    @Override
    public Set<String> getSupportedTags() {
        return SUPPORTED_TAGS;
    }
    
    @Override
    public boolean canConvert(Element element, ConversionContext context) {
        // Only convert if it's clearly a navigation element
        return super.canConvert(element, context) && isNavigationElement(element);
    }
    
    @Override
    public void convert(Element element, MarkdownBuilder builder, ConversionContext context) 
            throws ConversionException {
        validate(element, context);

        UIFramework framework = detectUIFramework(element);
        NavigationType navType = detectNavigationType(element, framework);
        
        switch (navType) {
            case BREADCRUMB:
                convertBreadcrumb(element, builder, context, framework);
                break;
            case MENU:
                convertMenu(element, builder, context, framework);
                break;
            case TABS:
                convertTabs(element, builder, context, framework);
                break;
            case NAVBAR:
                convertNavbar(element, builder, context, framework);
                break;
            case DROPDOWN:
                convertDropdown(element, builder, context, framework);
                break;
            default:
                convertGenericNavigation(element, builder, context, framework);
                break;
        }
    }
    
    @Override
    public int getPriority() {
        return 68; // Medium-high priority for navigation
    }
    
    /**
     * Enum for different types of navigation
     */
    private enum NavigationType {
        BREADCRUMB,
        MENU,
        TABS,
        NAVBAR,
        DROPDOWN,
        GENERIC
    }
    
    /**
     * Checks if an element is a navigation element
     */
    private boolean isNavigationElement(Element element) {
        String tagName = element.tagName().toLowerCase();
        
        // nav tag is always navigation
        if (TAG_NAV.equals(tagName)) {
            return true;
        }
        
        // Check for navigation-specific classes
        String className = element.className().toLowerCase();
        return NAVIGATION_CLASSES.stream().anyMatch(className::contains);
    }
    
    /**
     * Detects the type of navigation
     */
    private NavigationType detectNavigationType(Element element, UIFramework framework) {
        String className = element.className().toLowerCase();
        
        // Check for breadcrumb patterns
        if (className.contains("breadcrumb")) {
            return NavigationType.BREADCRUMB;
        }
        
        // Check for tab patterns
        if (className.contains("tab") || className.contains("nav-tabs") || className.contains("nav-pills")) {
            return NavigationType.TABS;
        }
        
        // Check for navbar patterns
        if (className.contains("navbar") || className.contains("app-bar")) {
            return NavigationType.NAVBAR;
        }
        
        // Check for dropdown patterns
        if (className.contains("dropdown")) {
            return NavigationType.DROPDOWN;
        }
        
        // Framework-specific detection
        switch (framework) {
            case ANT_DESIGN:
                if (className.contains("ant-breadcrumb")) return NavigationType.BREADCRUMB;
                if (className.contains("ant-tabs")) return NavigationType.TABS;
                if (className.contains("ant-dropdown")) return NavigationType.DROPDOWN;
                if (className.contains("ant-menu")) return NavigationType.MENU;
                break;
            case ELEMENT_UI:
                if (className.contains("el-breadcrumb")) return NavigationType.BREADCRUMB;
                if (className.contains("el-tabs")) return NavigationType.TABS;
                if (className.contains("el-dropdown")) return NavigationType.DROPDOWN;
                if (className.contains("el-menu")) return NavigationType.MENU;
                break;
            case VUETIFY:
                if (className.contains("v-breadcrumbs")) return NavigationType.BREADCRUMB;
                if (className.contains("v-tabs")) return NavigationType.TABS;
                if (className.contains("v-menu")) return NavigationType.DROPDOWN;
                if (className.contains("v-navigation-drawer")) return NavigationType.MENU;
                break;
            default:
                break;
        }
        
        return NavigationType.GENERIC;
    }
    
    /**
     * Converts breadcrumb navigation
     */
    private void convertBreadcrumb(Element element, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        if (!builder.isEmpty()) {
            builder.newline();
        }
        
        List<BreadcrumbItem> items = extractBreadcrumbItems(element, framework);
        
        if (!items.isEmpty()) {
            builder.append("**Navigation:** ");
            
            for (int i = 0; i < items.size(); i++) {
                BreadcrumbItem item = items.get(i);
                
                if (i > 0) {
                    builder.append(" > ");
                }
                
                if (!item.url.isEmpty()) {
                    builder.link(item.text, item.url);
                } else {
                    if (item.isActive) {
                        builder.append("**").append(item.text).append("**");
                    } else {
                        builder.append(item.text);
                    }
                }
            }
            
            builder.newline().newline();
        }
    }
    
    /**
     * Converts menu navigation
     */
    private void convertMenu(Element element, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        if (!builder.isEmpty()) {
            builder.newline();
        }
        
        String menuTitle = extractMenuTitle(element, framework);
        if (!menuTitle.isEmpty()) {
            builder.heading(3, menuTitle);
        }
        
        List<MenuItem> items = extractMenuItems(element, framework, 0);
        writeMenuItems(items, builder, 0);
        
        builder.newline();
    }
    
    /**
     * Converts tab navigation
     */
    private void convertTabs(Element element, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        if (!builder.isEmpty()) {
            builder.newline();
        }
        
        List<TabItem> tabs = extractTabItems(element, framework);
        
        if (!tabs.isEmpty()) {
            builder.append("**Tabs:** ");
            
            for (int i = 0; i < tabs.size(); i++) {
                TabItem tab = tabs.get(i);
                
                if (i > 0) {
                    builder.append(" | ");
                }
                
                if (tab.isActive) {
                    builder.append("**").append(tab.text).append("**");
                } else {
                    if (!tab.url.isEmpty()) {
                        builder.link(tab.text, tab.url);
                    } else {
                        builder.append(tab.text);
                    }
                }
                
                if (tab.isDisabled) {
                    builder.append(" (disabled)");
                }
            }
            
            builder.newline().newline();
        }
    }
    
    /**
     * Converts navbar navigation
     */
    private void convertNavbar(Element element, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        if (!builder.isEmpty()) {
            builder.newline();
        }
        
        // Extract brand/logo
        String brand = extractNavbarBrand(element, framework);
        if (!brand.isEmpty()) {
            builder.heading(2, brand);
        }
        
        // Extract navigation items
        List<MenuItem> items = extractNavbarItems(element, framework);
        if (!items.isEmpty()) {
            builder.append("**Navigation:** ");
            
            for (int i = 0; i < items.size(); i++) {
                MenuItem item = items.get(i);
                
                if (i > 0) {
                    builder.append(" | ");
                }
                
                if (!item.url.isEmpty()) {
                    builder.link(item.text, item.url);
                } else {
                    builder.append(item.text);
                }
                
                if (item.isActive) {
                    builder.append(" (current)");
                }
            }
            
            builder.newline();
        }
        
        builder.newline();
    }
    
    /**
     * Converts dropdown navigation
     */
    private void convertDropdown(Element element, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        String trigger = extractDropdownTrigger(element, framework);
        List<MenuItem> items = extractDropdownItems(element, framework);
        
        if (!trigger.isEmpty() && !items.isEmpty()) {
            builder.append("**").append(trigger).append("** ▼").newline();
            writeMenuItems(items, builder, 1);
        }
    }
    
    /**
     * Converts generic navigation
     */
    private void convertGenericNavigation(Element element, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        // For generic navigation, extract as a simple list
        List<MenuItem> items = extractMenuItems(element, framework, 0);
        if (!items.isEmpty()) {
            if (!builder.isEmpty()) {
                builder.newline();
            }
            writeMenuItems(items, builder, 0);
            builder.newline();
        }
    }
    
    // Data classes for navigation items
    
    private static class BreadcrumbItem {
        String text;
        String url;
        boolean isActive;
        
        BreadcrumbItem(String text, String url, boolean isActive) {
            this.text = text;
            this.url = url;
            this.isActive = isActive;
        }
    }
    
    private static class MenuItem {
        String text;
        String url;
        boolean isActive;
        boolean isDisabled;
        int level;
        List<MenuItem> children;
        
        MenuItem(String text, String url, boolean isActive, boolean isDisabled, int level) {
            this.text = text;
            this.url = url;
            this.isActive = isActive;
            this.isDisabled = isDisabled;
            this.level = level;
            this.children = new ArrayList<>();
        }
    }
    
    private static class TabItem {
        String text;
        String url;
        boolean isActive;
        boolean isDisabled;

        TabItem(String text, String url, boolean isActive, boolean isDisabled) {
            this.text = text;
            this.url = url;
            this.isActive = isActive;
            this.isDisabled = isDisabled;
        }
    }

    // Helper methods for navigation processing

    /**
     * Extracts breadcrumb items from the element
     */
    private List<BreadcrumbItem> extractBreadcrumbItems(Element element, UIFramework framework) {
        List<BreadcrumbItem> items = new ArrayList<>();

        Elements itemElements;
        switch (framework) {
            case ANT_DESIGN:
                itemElements = element.select(".ant-breadcrumb-link, .ant-breadcrumb-separator");
                break;
            case ELEMENT_UI:
                itemElements = element.select(".el-breadcrumb__item");
                break;
            case VUETIFY:
                itemElements = element.select(".v-breadcrumbs__item");
                break;
            case BOOTSTRAP:
                itemElements = element.select(".breadcrumb-item");
                break;
            default:
                itemElements = element.select("li, a, span");
                break;
        }

        for (Element item : itemElements) {
            // Skip separators
            if (item.hasClass("separator") || item.hasClass("ant-breadcrumb-separator")) {
                continue;
            }

            String text = extractTextContent(item);
            if (text.isEmpty()) {
                continue;
            }

            String url = EMPTY_STRING;
            Element link = item.selectFirst("a");
            if (link != null) {
                url = getAttribute(link, ATTR_HREF);
            } else if ("a".equals(item.tagName())) {
                url = getAttribute(item, ATTR_HREF);
            }

            Map<String, Boolean> state = extractElementState(item);
            boolean isActive = state.get("active") || item.hasClass("active") ||
                              item.hasClass("current") || item.hasClass("is-active");

            items.add(new BreadcrumbItem(text, url, isActive));
        }

        return items;
    }

    /**
     * Extracts menu items recursively
     */
    private List<MenuItem> extractMenuItems(Element element, UIFramework framework, int level) {
        List<MenuItem> items = new ArrayList<>();

        Elements itemElements;
        switch (framework) {
            case ANT_DESIGN:
                itemElements = element.select(level == 0 ? ".ant-menu-item, .ant-menu-submenu" : ".ant-menu-item");
                break;
            case ELEMENT_UI:
                itemElements = element.select(level == 0 ? ".el-menu-item, .el-submenu" : ".el-menu-item");
                break;
            case VUETIFY:
                itemElements = element.select(".v-list-item");
                break;
            default:
                itemElements = element.select("li");
                break;
        }

        for (Element item : itemElements) {
            String text = extractMenuItemText(item, framework);
            if (text.isEmpty()) {
                continue;
            }

            String url = extractMenuItemUrl(item, framework);
            Map<String, Boolean> state = extractElementState(item);
            boolean isActive = state.get("active");
            boolean isDisabled = state.get("disabled");

            MenuItem menuItem = new MenuItem(text, url, isActive, isDisabled, level);

            // Check for submenu
            Element submenu = findSubmenu(item, framework);
            if (submenu != null) {
                menuItem.children = extractMenuItems(submenu, framework, level + 1);
            }

            items.add(menuItem);
        }

        return items;
    }

    /**
     * Extracts tab items from the element
     */
    private List<TabItem> extractTabItems(Element element, UIFramework framework) {
        List<TabItem> items = new ArrayList<>();

        Elements tabElements;
        switch (framework) {
            case ANT_DESIGN:
                tabElements = element.select(".ant-tabs-tab");
                break;
            case ELEMENT_UI:
                tabElements = element.select(".el-tabs__item");
                break;
            case VUETIFY:
                tabElements = element.select(".v-tab");
                break;
            case BOOTSTRAP:
                tabElements = element.select(".nav-item, .nav-link");
                break;
            default:
                tabElements = element.select("li, a");
                break;
        }

        for (Element tab : tabElements) {
            String text = extractTextContent(tab);
            if (text.isEmpty()) {
                continue;
            }

            String url = EMPTY_STRING;
            Element link = tab.selectFirst("a");
            if (link != null) {
                url = getAttribute(link, ATTR_HREF);
            } else if ("a".equals(tab.tagName())) {
                url = getAttribute(tab, ATTR_HREF);
            }

            Map<String, Boolean> state = extractElementState(tab);
            boolean isActive = state.get("active");
            boolean isDisabled = state.get("disabled");

            items.add(new TabItem(text, url, isActive, isDisabled));
        }

        return items;
    }

    /**
     * Extracts navbar brand/logo
     */
    private String extractNavbarBrand(Element navbar, UIFramework framework) {
        Element brand = null;

        switch (framework) {
            case BOOTSTRAP:
                brand = navbar.selectFirst(".navbar-brand");
                break;
            case ANT_DESIGN:
                brand = navbar.selectFirst(".ant-menu-title, .ant-layout-header .logo");
                break;
            case ELEMENT_UI:
                brand = navbar.selectFirst(".el-menu-title");
                break;
            case VUETIFY:
                brand = navbar.selectFirst(".v-toolbar-title");
                break;
            default:
                brand = navbar.selectFirst(".brand, .logo, h1, h2");
                break;
        }

        return brand != null ? extractTextContent(brand) : EMPTY_STRING;
    }

    /**
     * Extracts navbar navigation items
     */
    private List<MenuItem> extractNavbarItems(Element navbar, UIFramework framework) {
        Element navContainer = null;

        switch (framework) {
            case BOOTSTRAP:
                navContainer = navbar.selectFirst(".navbar-nav");
                break;
            case ANT_DESIGN:
                navContainer = navbar.selectFirst(".ant-menu");
                break;
            case ELEMENT_UI:
                navContainer = navbar.selectFirst(".el-menu");
                break;
            default:
                navContainer = navbar.selectFirst("ul, .nav");
                break;
        }

        if (navContainer != null) {
            return extractMenuItems(navContainer, framework, 0);
        }

        return new ArrayList<>();
    }

    /**
     * Extracts dropdown trigger text
     */
    private String extractDropdownTrigger(Element dropdown, UIFramework framework) {
        Element trigger = null;

        switch (framework) {
            case ANT_DESIGN:
                trigger = dropdown.selectFirst(".ant-dropdown-trigger");
                break;
            case ELEMENT_UI:
                trigger = dropdown.selectFirst(".el-dropdown-trigger");
                break;
            case BOOTSTRAP:
                trigger = dropdown.selectFirst(".dropdown-toggle");
                break;
            default:
                trigger = dropdown.selectFirst("button, a");
                break;
        }

        return trigger != null ? extractTextContent(trigger) : EMPTY_STRING;
    }

    /**
     * Extracts dropdown menu items
     */
    private List<MenuItem> extractDropdownItems(Element dropdown, UIFramework framework) {
        Element menu = null;

        switch (framework) {
            case ANT_DESIGN:
                menu = dropdown.selectFirst(".ant-dropdown-menu");
                break;
            case ELEMENT_UI:
                menu = dropdown.selectFirst(".el-dropdown-menu");
                break;
            case BOOTSTRAP:
                menu = dropdown.selectFirst(".dropdown-menu");
                break;
            default:
                menu = dropdown.selectFirst("ul, .menu");
                break;
        }

        if (menu != null) {
            return extractMenuItems(menu, framework, 0);
        }

        return new ArrayList<>();
    }

    /**
     * Extracts menu item text
     */
    private String extractMenuItemText(Element item, UIFramework framework) {
        // Try to get text from link first
        Element link = item.selectFirst("a");
        if (link != null) {
            return extractTextContent(link);
        }

        // Framework-specific text extraction
        switch (framework) {
            case ANT_DESIGN:
                Element antText = item.selectFirst(".ant-menu-title-content");
                if (antText != null) {
                    return extractTextContent(antText);
                }
                break;
            case ELEMENT_UI:
                Element elText = item.selectFirst(".el-menu-item-title");
                if (elText != null) {
                    return extractTextContent(elText);
                }
                break;
            case VUETIFY:
                Element vText = item.selectFirst(".v-list-item__content");
                if (vText != null) {
                    return extractTextContent(vText);
                }
                break;
            default:
                break;
        }

        return extractTextContent(item);
    }

    /**
     * Extracts menu item URL
     */
    private String extractMenuItemUrl(Element item, UIFramework framework) {
        Element link = item.selectFirst("a");
        if (link != null) {
            return getAttribute(link, ATTR_HREF);
        }

        if ("a".equals(item.tagName())) {
            return getAttribute(item, ATTR_HREF);
        }

        return EMPTY_STRING;
    }

    /**
     * Finds submenu element
     */
    private Element findSubmenu(Element item, UIFramework framework) {
        switch (framework) {
            case ANT_DESIGN:
                return item.selectFirst(".ant-menu-sub");
            case ELEMENT_UI:
                return item.selectFirst(".el-submenu__popup");
            case VUETIFY:
                return item.selectFirst(".v-list-group__items");
            default:
                return item.selectFirst("ul");
        }
    }

    /**
     * Extracts menu title
     */
    private String extractMenuTitle(Element menu, UIFramework framework) {
        Element title = null;

        switch (framework) {
            case ANT_DESIGN:
                title = menu.selectFirst(".ant-menu-title");
                break;
            case ELEMENT_UI:
                title = menu.selectFirst(".el-menu-title");
                break;
            default:
                title = menu.selectFirst("h1, h2, h3, h4, h5, h6, .title");
                break;
        }

        return title != null ? extractTextContent(title) : EMPTY_STRING;
    }

    /**
     * Writes menu items to the builder
     */
    private void writeMenuItems(List<MenuItem> items, MarkdownBuilder builder, int baseLevel) {
        for (MenuItem item : items) {
            // Add indentation based on level
            for (int i = 0; i < baseLevel + item.level; i++) {
                builder.append("  ");
            }

            builder.append("- ");

            if (!item.url.isEmpty()) {
                builder.link(item.text, item.url);
            } else {
                builder.append(item.text);
            }

            if (item.isActive) {
                builder.append(" (current)");
            }

            if (item.isDisabled) {
                builder.append(" (disabled)");
            }

            builder.newline();

            // Write children
            if (!item.children.isEmpty()) {
                writeMenuItems(item.children, builder, baseLevel);
            }
        }
    }
}
