package com.talkweb.ai.indexer.util.markdown.converter;

import com.talkweb.ai.indexer.util.markdown.ConversionContext;
import com.talkweb.ai.indexer.util.markdown.MarkdownBuilder;
import com.talkweb.ai.indexer.util.markdown.converter.enhanced.EnhancedFormConverter;
import com.talkweb.ai.indexer.util.markdown.converter.enhanced.EnhancedNavigationConverter;
import com.talkweb.ai.indexer.util.markdown.converter.enhanced.EnhancedContainerConverter;
import org.jsoup.nodes.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Registry for managing element converters with caching and performance optimization
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class ConverterRegistry {
    
    private static final Logger logger = LoggerFactory.getLogger(ConverterRegistry.class);
    
    private final Map<String, List<ElementConverter>> convertersByTag;
    private final Map<String, ElementConverter> converterCache;
    private final List<ElementConverter> allConverters;
    private final int maxCacheSize;
    
    /**
     * Creates a new converter registry
     */
    public ConverterRegistry() {
        this(1000); // Default cache size
    }
    
    /**
     * Creates a new converter registry with specified cache size
     *
     * @param maxCacheSize the maximum cache size
     */
    public ConverterRegistry(int maxCacheSize) {
        this.convertersByTag = new ConcurrentHashMap<>();
        this.converterCache = new ConcurrentHashMap<>();
        this.allConverters = new CopyOnWriteArrayList<>();
        this.maxCacheSize = maxCacheSize;

        // Set up element processor to break circular dependency
        AbstractElementConverter.setElementProcessor((element, builder, context) -> {
            try {
                this.convert(element, builder, context);
            } catch (ElementConverter.ConversionException e) {
                logger.warn("Failed to convert child element: {}", element.tagName(), e);
            }
        });

        // Register default converters
        registerDefaultConverters();
    }
    
    /**
     * Registers a converter
     * 
     * @param converter the converter to register
     */
    public void register(ElementConverter converter) {
        if (converter == null) {
            throw new IllegalArgumentException("Converter cannot be null");
        }
        
        logger.debug("Registering converter: {} for tags: {}", 
                    converter.getClass().getSimpleName(), converter.getSupportedTags());
        
        allConverters.add(converter);
        
        // Register for each supported tag
        for (String tag : converter.getSupportedTags()) {
            convertersByTag.computeIfAbsent(tag.toLowerCase(), k -> new ArrayList<>()).add(converter);
        }
        
        // Sort converters by priority (highest first)
        for (List<ElementConverter> converters : convertersByTag.values()) {
            converters.sort((a, b) -> Integer.compare(b.getPriority(), a.getPriority()));
        }
        
        // Clear cache when new converter is registered
        clearCache();
    }
    
    /**
     * Unregisters a converter
     * 
     * @param converter the converter to unregister
     */
    public void unregister(ElementConverter converter) {
        if (converter == null) {
            return;
        }
        
        logger.debug("Unregistering converter: {}", converter.getClass().getSimpleName());
        
        allConverters.remove(converter);
        
        for (String tag : converter.getSupportedTags()) {
            List<ElementConverter> converters = convertersByTag.get(tag.toLowerCase());
            if (converters != null) {
                converters.remove(converter);
                if (converters.isEmpty()) {
                    convertersByTag.remove(tag.toLowerCase());
                }
            }
        }
        
        clearCache();
    }
    
    /**
     * Finds the best converter for an element
     * 
     * @param element the HTML element
     * @param context the conversion context
     * @return the best converter, or null if none found
     */
    public ElementConverter findConverter(Element element, ConversionContext context) {
        if (element == null) {
            return null;
        }
        
        String tagName = element.tagName().toLowerCase();
        String cacheKey = createCacheKey(tagName, context);
        
        // Check cache first
        ElementConverter cached = converterCache.get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        // Find converter
        ElementConverter converter = findConverterInternal(element, context, tagName);
        
        // Cache the result if cache is not full
        if (converter != null && converterCache.size() < maxCacheSize) {
            converterCache.put(cacheKey, converter);
        }
        
        return converter;
    }
    
    /**
     * Converts an element using the appropriate converter
     * 
     * @param element the HTML element
     * @param builder the markdown builder
     * @param context the conversion context
     * @throws ElementConverter.ConversionException if conversion fails
     */
    public void convert(Element element, MarkdownBuilder builder, ConversionContext context) 
            throws ElementConverter.ConversionException {
        
        ElementConverter converter = findConverter(element, context);
        if (converter != null) {
            try {
                converter.beforeConvert(element, builder, context);
                converter.convert(element, builder, context);
                converter.afterConvert(element, builder, context);
            } catch (Exception e) {
                logger.error("Error converting element: {}", element.tagName(), e);
                throw new ElementConverter.ConversionException(
                    "Failed to convert element: " + element.tagName(), e);
            }
        } else {
            // No specific converter found, use default behavior
            handleUnknownElement(element, builder, context);
        }
    }
    
    /**
     * Gets all registered converters
     * 
     * @return list of all converters
     */
    public List<ElementConverter> getAllConverters() {
        return new ArrayList<>(allConverters);
    }
    
    /**
     * Gets converters for a specific tag
     * 
     * @param tagName the tag name
     * @return list of converters for the tag
     */
    public List<ElementConverter> getConvertersForTag(String tagName) {
        List<ElementConverter> converters = convertersByTag.get(tagName.toLowerCase());
        return converters != null ? new ArrayList<>(converters) : Collections.emptyList();
    }
    
    /**
     * Clears the converter cache
     */
    public void clearCache() {
        converterCache.clear();
        logger.debug("Converter cache cleared");
    }
    
    /**
     * Gets cache statistics
     * 
     * @return map of cache statistics
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cacheSize", converterCache.size());
        stats.put("maxCacheSize", maxCacheSize);
        stats.put("registeredConverters", allConverters.size());
        stats.put("supportedTags", convertersByTag.keySet().size());
        return stats;
    }
    
    /**
     * Registers default converters
     */
    private void registerDefaultConverters() {
        // Register enhanced converters first (higher priority)
        register(new EnhancedFormConverter());
        register(new EnhancedNavigationConverter());
        register(new EnhancedContainerConverter());

        // Register standard converters
        register(new HeadingConverter());
        register(new TextFormattingConverter());
        register(new LinkConverter());
        register(new TableConverter());
        register(new ListConverter());
        register(new CodeConverter());
        register(new MediaConverter());
        register(new BlockElementConverter());
    }
    
    /**
     * Internal method to find converter
     */
    private ElementConverter findConverterInternal(Element element, ConversionContext context, String tagName) {
        List<ElementConverter> candidates = convertersByTag.get(tagName);
        if (candidates == null || candidates.isEmpty()) {
            return null;
        }
        
        // Find the first converter that can handle this element
        for (ElementConverter converter : candidates) {
            if (converter.canConvert(element, context)) {
                return converter;
            }
        }
        
        return null;
    }
    
    /**
     * Creates a cache key for an element and context
     */
    private String createCacheKey(String tagName, ConversionContext context) {
        // Simple cache key - can be enhanced with more context information
        return tagName + "_" + context.getMode().name();
    }
    
    /**
     * Handles unknown elements that don't have specific converters
     */
    private void handleUnknownElement(Element element, MarkdownBuilder builder, ConversionContext context) {
        String tagName = element.tagName().toLowerCase();
        
        // For container elements, just process children
        if (isContainerElement(tagName)) {
            processElementChildren(element, builder, context);
        } else {
            logger.debug("No converter found for element: {}", tagName);
            // For unknown elements, process children as fallback
            processElementChildren(element, builder, context);
        }
    }
    
    /**
     * Checks if an element is a generic container
     */
    private boolean isContainerElement(String tagName) {
        return "div".equals(tagName) || "span".equals(tagName) || "section".equals(tagName) ||
               "article".equals(tagName) || "header".equals(tagName) || "footer".equals(tagName) ||
               "main".equals(tagName) || "aside".equals(tagName) || "nav".equals(tagName);
    }
    
    /**
     * Processes children of an element
     */
    private void processElementChildren(Element element, MarkdownBuilder builder, ConversionContext context) {
        context.pushElement(element);
        try {
            for (Element child : element.children()) {
                convert(child, builder, context);
            }
        } catch (ElementConverter.ConversionException e) {
            logger.error("Error processing children of element: {}", element.tagName(), e);
        } finally {
            context.popElement();
        }
    }
}
