package com.talkweb.ai.indexer.util.markdown.converter.enhanced;

import com.talkweb.ai.indexer.util.markdown.ConversionContext;
import com.talkweb.ai.indexer.util.markdown.MarkdownBuilder;
import com.talkweb.ai.indexer.util.markdown.converter.EnhancedElementConverter;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.*;

import static com.talkweb.ai.indexer.util.markdown.MarkdownConstants.*;

/**
 * Enhanced converter for HTML form elements with framework compatibility
 * 
 * Supports:
 * - Bootstrap forms (form-control, form-group, input-group, etc.)
 * - Ant Design forms (ant-form, ant-input, ant-select, etc.)
 * - Element UI forms (el-form, el-input, el-select, etc.)
 * - Vuetify forms (v-form, v-text-field, v-select, etc.)
 * - Material-UI forms (MuiTextField, MuiSelect, etc.)
 * - Semantic UI forms (ui form, field, etc.)
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class EnhancedFormConverter extends EnhancedElementConverter {
    
    private static final Set<String> SUPPORTED_TAGS = Set.of(
        TAG_FORM, TAG_INPUT, TAG_SELECT, TAG_TEXTAREA, TAG_BUTTON, 
        TAG_LABEL, "fieldset", "legend", "option", "optgroup"
    );
    
    @Override
    public Set<String> getSupportedTags() {
        return SUPPORTED_TAGS;
    }
    
    @Override
    public void convert(Element element, MarkdownBuilder builder, ConversionContext context) 
            throws ConversionException {
        validate(element, context);
        
        String tagName = element.tagName().toLowerCase();
        UIFramework framework = detectUIFramework(element);
        
        switch (tagName) {
            case TAG_FORM:
                convertForm(element, builder, context, framework);
                break;
            case TAG_INPUT:
                convertInput(element, builder, context, framework);
                break;
            case TAG_SELECT:
                convertSelect(element, builder, context, framework);
                break;
            case TAG_TEXTAREA:
                convertTextarea(element, builder, context, framework);
                break;
            case TAG_BUTTON:
                convertButton(element, builder, context, framework);
                break;
            case TAG_LABEL:
                convertLabel(element, builder, context, framework);
                break;
            case "fieldset":
                convertFieldset(element, builder, context, framework);
                break;
            case "legend":
                convertLegend(element, builder, context, framework);
                break;
            default:
                // For other form elements, use default processing
                processChildren(element, builder, context);
                break;
        }
    }
    
    @Override
    public int getPriority() {
        return 65; // Medium-high priority for forms
    }
    
    /**
     * Converts form elements with enhanced structure preservation
     */
    private void convertForm(Element form, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        if (!builder.isEmpty()) {
            builder.newline();
        }
        
        // Extract form title/heading
        String formTitle = extractFormTitle(form, framework);
        if (!formTitle.isEmpty()) {
            builder.heading(3, formTitle);
        }
        
        // Process form fields with structure preservation
        List<FormField> fields = extractFormFields(form, framework);
        
        if (!fields.isEmpty()) {
            builder.newline().append("**Form Fields:**").newline().newline();
            
            for (FormField field : fields) {
                writeFormField(field, builder);
            }
        }
        
        builder.newline();
    }
    
    /**
     * Converts input elements with framework-specific enhancements
     */
    private void convertInput(Element input, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        String type = getAttribute(input, "type");
        String name = getAttribute(input, "name");
        String value = getAttribute(input, "value");
        String placeholder = getAttribute(input, "placeholder");
        
        Map<String, Boolean> state = extractElementState(input);
        
        // Extract label if available
        String label = findAssociatedLabel(input, framework);
        
        StringBuilder inputText = new StringBuilder();
        
        if (!label.isEmpty()) {
            inputText.append("**").append(label).append("**");
            if (state.get("disabled")) {
                inputText.append(" (disabled)");
            }
            if (isRequired(input, framework)) {
                inputText.append(" *");
            }
            inputText.append(": ");
        }
        
        switch (type.toLowerCase()) {
            case "checkbox":
            case "radio":
                inputText.append(state.get("checked") ? "☑" : "☐").append(" ");
                if (!value.isEmpty()) {
                    inputText.append(value);
                }
                break;
            case "submit":
            case "button":
                inputText.append("[").append(!value.isEmpty() ? value : "Submit").append("]");
                break;
            case "file":
                inputText.append("📁 File upload");
                if (!placeholder.isEmpty()) {
                    inputText.append(" (").append(placeholder).append(")");
                }
                break;
            case "password":
                inputText.append("🔒 Password field");
                break;
            case "email":
                inputText.append("📧 ").append(!value.isEmpty() ? value : (placeholder.isEmpty() ? "Email" : placeholder));
                break;
            case "tel":
                inputText.append("📞 ").append(!value.isEmpty() ? value : (placeholder.isEmpty() ? "Phone" : placeholder));
                break;
            case "url":
                inputText.append("🔗 ").append(!value.isEmpty() ? value : (placeholder.isEmpty() ? "URL" : placeholder));
                break;
            case "date":
                inputText.append("📅 ").append(!value.isEmpty() ? value : "Date");
                break;
            case "time":
                inputText.append("🕐 ").append(!value.isEmpty() ? value : "Time");
                break;
            case "number":
            case "range":
                inputText.append("🔢 ").append(!value.isEmpty() ? value : (placeholder.isEmpty() ? "Number" : placeholder));
                break;
            default: // text, search, etc.
                if (!value.isEmpty()) {
                    inputText.append(value);
                } else if (!placeholder.isEmpty()) {
                    inputText.append("_").append(placeholder).append("_");
                } else {
                    inputText.append("_Text input_");
                }
                break;
        }
        
        builder.append(inputText.toString());
        
        // Add validation state if present
        String validationState = extractValidationState(input, framework);
        if (!validationState.isEmpty()) {
            builder.append(" ").append(validationState);
        }
        
        builder.newline();
    }
    
    /**
     * Converts select elements with options
     */
    private void convertSelect(Element select, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        String label = findAssociatedLabel(select, framework);
        Map<String, Boolean> state = extractElementState(select);
        
        if (!label.isEmpty()) {
            builder.append("**").append(label).append("**");
            if (state.get("disabled")) {
                builder.append(" (disabled)");
            }
            if (isRequired(select, framework)) {
                builder.append(" *");
            }
            builder.append(": ");
        }
        
        // Extract selected option
        Element selectedOption = select.selectFirst("option[selected]");
        if (selectedOption != null) {
            builder.append(selectedOption.text());
        } else {
            Elements options = select.select("option");
            if (!options.isEmpty()) {
                builder.append("_Select option_");
                
                // List available options
                if (options.size() <= 5) { // Only show options for small selects
                    builder.append(" (");
                    List<String> optionTexts = new ArrayList<>();
                    for (Element option : options) {
                        String optionText = option.text().trim();
                        if (!optionText.isEmpty() && !optionText.equals("Select...") && !optionText.equals("Choose...")) {
                            optionTexts.add(optionText);
                        }
                    }
                    builder.append(String.join(", ", optionTexts));
                    builder.append(")");
                }
            }
        }
        
        builder.newline();
    }
    
    /**
     * Converts textarea elements
     */
    private void convertTextarea(Element textarea, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        String label = findAssociatedLabel(textarea, framework);
        String value = textarea.text();
        String placeholder = getAttribute(textarea, "placeholder");
        Map<String, Boolean> state = extractElementState(textarea);
        
        if (!label.isEmpty()) {
            builder.append("**").append(label).append("**");
            if (state.get("disabled")) {
                builder.append(" (disabled)");
            }
            if (isRequired(textarea, framework)) {
                builder.append(" *");
            }
            builder.append(": ");
        }
        
        if (!value.isEmpty()) {
            builder.newline().append("```").newline().append(value).newline().append("```");
        } else if (!placeholder.isEmpty()) {
            builder.append("_").append(placeholder).append("_");
        } else {
            builder.append("_Text area_");
        }
        
        builder.newline();
    }
    
    /**
     * Converts button elements with framework-specific styling
     */
    private void convertButton(Element button, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        String text = extractButtonText(button, framework);
        String type = getAttribute(button, "type");
        Map<String, Boolean> state = extractElementState(button);
        
        if (text.isEmpty()) {
            text = "Button";
        }
        
        // Add button styling based on framework
        String buttonStyle = extractButtonStyle(button, framework);
        
        builder.append("[").append(text);
        
        if (!buttonStyle.isEmpty()) {
            builder.append(" (").append(buttonStyle).append(")");
        }
        
        if (state.get("disabled")) {
            builder.append(" - disabled");
        }
        
        builder.append("]");
        
        if ("submit".equals(type)) {
            builder.append(" 📤");
        }
        
        builder.newline();
    }
    
    /**
     * Converts label elements
     */
    private void convertLabel(Element label, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        String text = extractTextContent(label);
        if (!text.isEmpty()) {
            builder.append("**").append(text).append("**");
            
            // Check if this is a required field label
            Element associatedInput = findAssociatedInput(label);
            if (associatedInput != null && isRequired(associatedInput, framework)) {
                builder.append(" *");
            }
            
            builder.append(": ");
        }
    }
    
    /**
     * Converts fieldset elements with legend
     */
    private void convertFieldset(Element fieldset, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        Element legend = fieldset.selectFirst("legend");
        if (legend != null) {
            String legendText = extractTextContent(legend);
            if (!legendText.isEmpty()) {
                builder.newline().heading(4, legendText);
            }
        }
        
        processChildren(fieldset, builder, context);
        builder.newline();
    }
    
    /**
     * Converts legend elements
     */
    private void convertLegend(Element legend, MarkdownBuilder builder, ConversionContext context, UIFramework framework) {
        // Legend is handled by fieldset converter
    }
    
    // Helper methods for form processing
    
    /**
     * Data class for form field information
     */
    private static class FormField {
        String label;
        String type;
        String value;
        boolean required;
        boolean disabled;
        String validationState;
        
        FormField(String label, String type, String value, boolean required, boolean disabled, String validationState) {
            this.label = label;
            this.type = type;
            this.value = value;
            this.required = required;
            this.disabled = disabled;
            this.validationState = validationState;
        }
    }
    
    /**
     * Extracts form title from various sources
     */
    private String extractFormTitle(Element form, UIFramework framework) {
        // Try to find title in various ways
        Element title = form.selectFirst("h1, h2, h3, h4, h5, h6");
        if (title != null) {
            return extractTextContent(title);
        }
        
        // Check for framework-specific title elements
        switch (framework) {
            case ANT_DESIGN:
                Element antTitle = form.selectFirst(".ant-form-title, .ant-card-head-title");
                if (antTitle != null) {
                    return extractTextContent(antTitle);
                }
                break;
            case ELEMENT_UI:
                Element elTitle = form.selectFirst(".el-form-title, .el-card__header");
                if (elTitle != null) {
                    return extractTextContent(elTitle);
                }
                break;
            case VUETIFY:
                Element vTitle = form.selectFirst(".v-card-title, .v-toolbar-title");
                if (vTitle != null) {
                    return extractTextContent(vTitle);
                }
                break;
            default:
                // For other frameworks, no specific title extraction
                break;
        }
        
        // Check form attributes
        String formTitle = getAttribute(form, "title");
        if (!formTitle.isEmpty()) {
            return formTitle;
        }
        
        return EMPTY_STRING;
    }
    
    /**
     * Extracts form fields with their metadata
     */
    private List<FormField> extractFormFields(Element form, UIFramework framework) {
        List<FormField> fields = new ArrayList<>();
        
        Elements inputs = form.select("input, select, textarea");
        for (Element input : inputs) {
            String type = input.tagName().toLowerCase();
            if ("input".equals(type)) {
                type = getAttribute(input, "type");
            }
            
            String label = findAssociatedLabel(input, framework);
            String value = extractFieldValue(input);
            boolean required = isRequired(input, framework);
            boolean disabled = extractElementState(input).get("disabled");
            String validationState = extractValidationState(input, framework);
            
            fields.add(new FormField(label, type, value, required, disabled, validationState));
        }
        
        return fields;
    }
    
    /**
     * Writes a form field to the builder
     */
    private void writeFormField(FormField field, MarkdownBuilder builder) {
        builder.append("- ");

        if (!field.label.isEmpty()) {
            builder.append("**").append(field.label).append("**");
            if (field.required) {
                builder.append(" *");
            }
            if (field.disabled) {
                builder.append(" (disabled)");
            }
            builder.append(": ");
        }

        if (!field.value.isEmpty()) {
            builder.append(field.value);
        } else {
            builder.append("_").append(field.type).append(" field_");
        }

        if (!field.validationState.isEmpty()) {
            builder.append(" ").append(field.validationState);
        }

        builder.newline();
    }

    /**
     * Finds the associated label for an input element
     */
    private String findAssociatedLabel(Element input, UIFramework framework) {
        String id = getAttribute(input, "id");
        String name = getAttribute(input, "name");

        // Try to find label by 'for' attribute
        if (!id.isEmpty()) {
            Element label = input.ownerDocument().selectFirst("label[for=" + id + "]");
            if (label != null) {
                return extractTextContent(label);
            }
        }

        // Try to find parent label
        Element parentLabel = input.closest("label");
        if (parentLabel != null) {
            return extractTextContent(parentLabel);
        }

        // Framework-specific label finding
        switch (framework) {
            case ANT_DESIGN:
                Element antLabel = input.closest(".ant-form-item").selectFirst(".ant-form-item-label label");
                if (antLabel != null) {
                    return extractTextContent(antLabel);
                }
                break;
            case ELEMENT_UI:
                Element elLabel = input.closest(".el-form-item").selectFirst(".el-form-item__label");
                if (elLabel != null) {
                    return extractTextContent(elLabel);
                }
                break;
            case VUETIFY:
                Element vLabel = input.closest(".v-input").selectFirst(".v-label");
                if (vLabel != null) {
                    return extractTextContent(vLabel);
                }
                break;
            case BOOTSTRAP:
                Element bsLabel = input.closest(".form-group, .mb-3").selectFirst("label");
                if (bsLabel != null) {
                    return extractTextContent(bsLabel);
                }
                break;
            default:
                // For other frameworks, use standard label finding
                break;
        }

        // Try to find nearby text that might be a label
        Element prev = input.previousElementSibling();
        if (prev != null && ("label".equals(prev.tagName()) || prev.hasClass("label"))) {
            return extractTextContent(prev);
        }

        return EMPTY_STRING;
    }

    /**
     * Finds the associated input for a label element
     */
    private Element findAssociatedInput(Element label) {
        String forAttr = getAttribute(label, "for");
        if (!forAttr.isEmpty()) {
            return label.ownerDocument().selectFirst("#" + forAttr);
        }

        // Check for nested input
        Element input = label.selectFirst("input, select, textarea");
        if (input != null) {
            return input;
        }

        // Check next sibling
        Element next = label.nextElementSibling();
        if (next != null && ("input".equals(next.tagName()) || "select".equals(next.tagName()) || "textarea".equals(next.tagName()))) {
            return next;
        }

        return null;
    }

    /**
     * Checks if a field is required
     */
    private boolean isRequired(Element input, UIFramework framework) {
        if (input.hasAttr("required")) {
            return true;
        }

        // Framework-specific required indicators
        switch (framework) {
            case ANT_DESIGN:
                Element antFormItem = input.closest(".ant-form-item");
                return antFormItem != null && antFormItem.hasClass("ant-form-item-required");
            case ELEMENT_UI:
                Element elFormItem = input.closest(".el-form-item");
                return elFormItem != null && elFormItem.hasClass("is-required");
            case VUETIFY:
                return input.closest(".v-input").hasClass("v-input--is-required");
            default:
                // For other frameworks, rely on HTML required attribute
                break;
        }

        return false;
    }

    /**
     * Extracts validation state from an input
     */
    private String extractValidationState(Element input, UIFramework framework) {
        switch (framework) {
            case ANT_DESIGN:
                Element antFormItem = input.closest(".ant-form-item");
                if (antFormItem != null) {
                    if (antFormItem.hasClass("ant-form-item-has-error")) {
                        Element errorMsg = antFormItem.selectFirst(".ant-form-item-explain");
                        return "❌" + (errorMsg != null ? " " + errorMsg.text() : "");
                    }
                    if (antFormItem.hasClass("ant-form-item-has-success")) {
                        return "✅";
                    }
                    if (antFormItem.hasClass("ant-form-item-has-warning")) {
                        return "⚠️";
                    }
                }
                break;
            case ELEMENT_UI:
                Element elFormItem = input.closest(".el-form-item");
                if (elFormItem != null) {
                    if (elFormItem.hasClass("is-error")) {
                        Element errorMsg = elFormItem.selectFirst(".el-form-item__error");
                        return "❌" + (errorMsg != null ? " " + errorMsg.text() : "");
                    }
                    if (elFormItem.hasClass("is-success")) {
                        return "✅";
                    }
                }
                break;
            case BOOTSTRAP:
                if (input.hasClass("is-invalid")) {
                    Element feedback = input.parent().selectFirst(".invalid-feedback");
                    return "❌" + (feedback != null ? " " + feedback.text() : "");
                }
                if (input.hasClass("is-valid")) {
                    return "✅";
                }
                break;
            default:
                // For other frameworks, no specific validation state extraction
                break;
        }

        return EMPTY_STRING;
    }

    /**
     * Extracts field value based on input type
     */
    private String extractFieldValue(Element input) {
        String tagName = input.tagName().toLowerCase();

        switch (tagName) {
            case "input":
                String type = getAttribute(input, "type").toLowerCase();
                if ("checkbox".equals(type) || "radio".equals(type)) {
                    return input.hasAttr("checked") ? getAttribute(input, "value") : EMPTY_STRING;
                }
                return getAttribute(input, "value");
            case "select":
                Element selected = input.selectFirst("option[selected]");
                return selected != null ? selected.text() : EMPTY_STRING;
            case "textarea":
                return input.text();
            default:
                return EMPTY_STRING;
        }
    }

    /**
     * Extracts button text with framework-specific handling
     */
    private String extractButtonText(Element button, UIFramework framework) {
        // Try direct text first
        String text = button.ownText();
        if (!text.isEmpty()) {
            return text;
        }

        // Framework-specific text extraction
        switch (framework) {
            case ANT_DESIGN:
                Element antText = button.selectFirst(".ant-btn-content");
                if (antText != null) {
                    return antText.text();
                }
                break;
            case ELEMENT_UI:
                Element elText = button.selectFirst(".el-button-content");
                if (elText != null) {
                    return elText.text();
                }
                break;
            case VUETIFY:
                Element vText = button.selectFirst(".v-btn__content");
                if (vText != null) {
                    return vText.text();
                }
                break;
            default:
                // For other frameworks, use standard text extraction
                break;
        }

        // Fallback to full text content
        return extractTextContent(button);
    }

    /**
     * Extracts button style information
     */
    private String extractButtonStyle(Element button, UIFramework framework) {
        String className = button.className().toLowerCase();

        switch (framework) {
            case BOOTSTRAP:
                if (className.contains("btn-primary")) return "primary";
                if (className.contains("btn-secondary")) return "secondary";
                if (className.contains("btn-success")) return "success";
                if (className.contains("btn-danger")) return "danger";
                if (className.contains("btn-warning")) return "warning";
                if (className.contains("btn-info")) return "info";
                if (className.contains("btn-light")) return "light";
                if (className.contains("btn-dark")) return "dark";
                break;
            case ANT_DESIGN:
                if (className.contains("ant-btn-primary")) return "primary";
                if (className.contains("ant-btn-danger")) return "danger";
                if (className.contains("ant-btn-dashed")) return "dashed";
                if (className.contains("ant-btn-link")) return "link";
                break;
            case ELEMENT_UI:
                if (className.contains("el-button--primary")) return "primary";
                if (className.contains("el-button--success")) return "success";
                if (className.contains("el-button--warning")) return "warning";
                if (className.contains("el-button--danger")) return "danger";
                if (className.contains("el-button--info")) return "info";
                break;
            default:
                // For other frameworks, no specific style extraction
                break;
        }

        return EMPTY_STRING;
    }
}
