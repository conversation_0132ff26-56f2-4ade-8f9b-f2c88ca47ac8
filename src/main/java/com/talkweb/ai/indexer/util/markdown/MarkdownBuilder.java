package com.talkweb.ai.indexer.util.markdown;

import java.util.regex.Pattern;

import static com.talkweb.ai.indexer.util.markdown.MarkdownConstants.*;

/**
 * High-performance Markdown builder with optimized string operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class MarkdownBuilder {
    
    private final StringBuilder buffer;
    private static final Pattern WHITESPACE_REGEX = Pattern.compile(WHITESPACE_PATTERN);
    private static final Pattern EXCESSIVE_NEWLINES_REGEX = Pattern.compile(EXCESSIVE_NEWLINES_PATTERN);
    private static final Pattern TRAILING_WHITESPACE_REGEX = Pattern.compile(TRAILING_WHITESPACE_PATTERN);
    
    /**
     * Creates a new MarkdownBuilder with default initial capacity
     */
    public MarkdownBuilder() {
        this(DEFAULT_INITIAL_CAPACITY);
    }
    
    /**
     * Creates a new MarkdownBuilder with specified initial capacity
     * 
     * @param initialCapacity the initial capacity of the internal buffer
     */
    public MarkdownBuilder(int initialCapacity) {
        this.buffer = new StringBuilder(initialCapacity);
    }
    
    /**
     * Appends text to the markdown
     * 
     * @param text the text to append
     * @return this builder for method chaining
     */
    public MarkdownBuilder append(String text) {
        if (text != null) {
            buffer.append(text);
        }
        return this;
    }
    
    /**
     * Appends a character to the markdown
     * 
     * @param ch the character to append
     * @return this builder for method chaining
     */
    public MarkdownBuilder append(char ch) {
        buffer.append(ch);
        return this;
    }
    
    /**
     * Appends a newline
     * 
     * @return this builder for method chaining
     */
    public MarkdownBuilder newline() {
        buffer.append(NEWLINE);
        return this;
    }
    
    /**
     * Appends a double newline (paragraph break)
     * 
     * @return this builder for method chaining
     */
    public MarkdownBuilder paragraph() {
        buffer.append(DOUBLE_NEWLINE);
        return this;
    }
    
    /**
     * Appends a space
     * 
     * @return this builder for method chaining
     */
    public MarkdownBuilder space() {
        buffer.append(SPACE);
        return this;
    }
    
    /**
     * Appends a heading with the specified level
     *
     * @param level the heading level (1-6)
     * @param text the heading text
     * @return this builder for method chaining
     */
    public MarkdownBuilder heading(int level, String text) {
        if (level < 1 || level > 6) {
            throw new IllegalArgumentException("Heading level must be between 1 and 6");
        }

        for (int i = 0; i < level; i++) {
            append(HEADING_PREFIX);
        }
        space().append(text).newline();
        return this;
    }
    
    /**
     * Appends bold text
     * 
     * @param text the text to make bold
     * @return this builder for method chaining
     */
    public MarkdownBuilder bold(String text) {
        return append(BOLD_MARKER).append(text).append(BOLD_MARKER);
    }
    
    /**
     * Appends italic text
     * 
     * @param text the text to make italic
     * @return this builder for method chaining
     */
    public MarkdownBuilder italic(String text) {
        return append(ITALIC_MARKER).append(text).append(ITALIC_MARKER);
    }
    
    /**
     * Appends inline code
     * 
     * @param code the code text
     * @return this builder for method chaining
     */
    public MarkdownBuilder code(String code) {
        return append(CODE_MARKER).append(code).append(CODE_MARKER);
    }
    
    /**
     * Appends a code block
     * 
     * @param code the code content
     * @param language the programming language (optional)
     * @return this builder for method chaining
     */
    public MarkdownBuilder codeBlock(String code, String language) {
        newline().append(CODE_BLOCK_MARKER);
        if (language != null && !language.trim().isEmpty()) {
            append(language.trim());
        }
        newline().append(code).newline().append(CODE_BLOCK_MARKER).paragraph();
        return this;
    }
    
    /**
     * Appends a code block without language specification
     * 
     * @param code the code content
     * @return this builder for method chaining
     */
    public MarkdownBuilder codeBlock(String code) {
        return codeBlock(code, null);
    }
    
    /**
     * Appends a link
     * 
     * @param text the link text
     * @param url the link URL
     * @param title the link title (optional)
     * @return this builder for method chaining
     */
    public MarkdownBuilder link(String text, String url, String title) {
        append("[").append(text).append("](").append(url);
        if (title != null && !title.trim().isEmpty()) {
            append(" \"").append(title.trim()).append("\"");
        }
        append(")");
        return this;
    }
    
    /**
     * Appends a link without title
     * 
     * @param text the link text
     * @param url the link URL
     * @return this builder for method chaining
     */
    public MarkdownBuilder link(String text, String url) {
        return link(text, url, null);
    }
    
    /**
     * Appends an image
     * 
     * @param alt the alt text
     * @param src the image source URL
     * @param title the image title (optional)
     * @return this builder for method chaining
     */
    public MarkdownBuilder image(String alt, String src, String title) {
        append("![").append(alt).append("](").append(src);
        if (title != null && !title.trim().isEmpty()) {
            append(" \"").append(title.trim()).append("\"");
        }
        append(")");
        return this;
    }
    
    /**
     * Appends an image without title
     * 
     * @param alt the alt text
     * @param src the image source URL
     * @return this builder for method chaining
     */
    public MarkdownBuilder image(String alt, String src) {
        return image(alt, src, null);
    }
    
    /**
     * Appends a blockquote line
     * 
     * @param text the quote text
     * @return this builder for method chaining
     */
    public MarkdownBuilder blockquote(String text) {
        return append(BLOCKQUOTE_PREFIX).append(text).newline();
    }
    
    /**
     * Appends a list item
     * 
     * @param text the item text
     * @param ordered whether this is an ordered list item
     * @param index the item index (for ordered lists)
     * @param depth the nesting depth
     * @return this builder for method chaining
     */
    public MarkdownBuilder listItem(String text, boolean ordered, int index, int depth) {
        // Add indentation for nested lists
        for (int i = 0; i < depth; i++) {
            append("  ");
        }
        
        if (ordered) {
            append(String.valueOf(index)).append(". ");
        } else {
            append(UNORDERED_LIST_MARKER);
        }
        
        append(text).newline();
        return this;
    }
    
    /**
     * Appends a horizontal rule
     * 
     * @return this builder for method chaining
     */
    public MarkdownBuilder horizontalRule() {
        return newline().append(HORIZONTAL_RULE).paragraph();
    }
    
    /**
     * Appends a line break (two spaces + newline)
     * 
     * @return this builder for method chaining
     */
    public MarkdownBuilder lineBreak() {
        return append(LINE_BREAK);
    }
    
    /**
     * Normalizes whitespace in the current content
     *
     * @return this builder for method chaining
     */
    public MarkdownBuilder normalizeWhitespace() {
        String content = buffer.toString();

        // Don't normalize if content is empty
        if (content.trim().isEmpty()) {
            return this;
        }

        // Remove excessive newlines (more than 2)
        content = EXCESSIVE_NEWLINES_REGEX.matcher(content).replaceAll(DOUBLE_NEWLINE);

        // Remove trailing whitespace from lines
        String[] lines = content.split(NEWLINE);
        buffer.setLength(0);

        for (int i = 0; i < lines.length; i++) {
            String line = TRAILING_WHITESPACE_REGEX.matcher(lines[i]).replaceAll(EMPTY_STRING);
            buffer.append(line);
            if (i < lines.length - 1) {
                buffer.append(NEWLINE);
            }
        }

        return this;
    }
    
    /**
     * Clears the builder content
     * 
     * @return this builder for method chaining
     */
    public MarkdownBuilder clear() {
        buffer.setLength(0);
        return this;
    }
    
    /**
     * Returns the current length of the content
     * 
     * @return the content length
     */
    public int length() {
        return buffer.length();
    }
    
    /**
     * Checks if the builder is empty
     * 
     * @return true if empty, false otherwise
     */
    public boolean isEmpty() {
        return buffer.length() == 0;
    }
    
    /**
     * Returns the markdown content as a string
     * 
     * @return the markdown content
     */
    @Override
    public String toString() {
        return buffer.toString().trim();
    }
}
