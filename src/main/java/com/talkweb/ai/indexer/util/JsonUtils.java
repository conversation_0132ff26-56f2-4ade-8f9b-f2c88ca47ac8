package com.talkweb.ai.indexer.util;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * JSON工具类
 */
public final class JsonUtils {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private JsonUtils() {
        // 工具类，防止实例化
    }

    /**
     * 加载JSON文件
     * @param file JSON文件
     * @return JSON内容
     * @throws IOException 如果加载失败
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> loadJson(File file) throws IOException {
        Object loaded = OBJECT_MAPPER.readValue(file, Object.class);
        if (loaded instanceof Map) {
            return (Map<String, Object>) loaded;
        }
        return Collections.emptyMap();
    }

    /**
     * 将嵌套的JSON映射转换为扁平的属性映射
     * @param jsonMap JSON映射
     * @return 扁平的属性映射
     */
    public static Map<String, String> flattenJsonMap(Map<String, Object> jsonMap) {
        Map<String, String> result = new HashMap<>();
        flattenJsonMapRecursive(jsonMap, "", result);
        return result;
    }

    private static void flattenJsonMapRecursive(Map<String, Object> jsonMap, String prefix, Map<String, String> result) {
        for (Map.Entry<String, Object> entry : jsonMap.entrySet()) {
            String key = prefix.isEmpty() ? entry.getKey() : prefix + "." + entry.getKey();
            Object value = entry.getValue();

            if (value instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> nestedMap = (Map<String, Object>) value;
                flattenJsonMapRecursive(nestedMap, key, result);
            } else {
                result.put(key, value != null ? value.toString() : "");
            }
        }
    }
}