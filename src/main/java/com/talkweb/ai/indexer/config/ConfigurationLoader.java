package com.talkweb.ai.indexer.config;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Path;

/**
 * 配置加载器接口
 */
public interface ConfigurationLoader {
    
    /**
     * 从输入流加载配置
     */
    AppConfiguration load(InputStream input) throws IOException, ConfigurationException;
    
    /**
     * 从文件加载配置
     */
    default AppConfiguration load(Path file) throws IOException, ConfigurationException {
        try (InputStream input = java.nio.file.Files.newInputStream(file)) {
            return load(input);
        }
    }
    
    /**
     * 从类路径资源加载配置
     */
    default AppConfiguration loadFromClasspath(String resource) throws IOException, ConfigurationException {
        try (InputStream input = getClass().getClassLoader().getResourceAsStream(resource)) {
            if (input == null) {
                throw new ConfigurationException("Resource not found: " + resource);
            }
            return load(input);
        }
    }

    /**
     * 从JSON字符串加载配置
     */
    AppConfiguration fromJson(String json) throws IOException, ConfigurationException;

    /**
     * 从YAML字符串加载配置
     */
    AppConfiguration fromYaml(String yaml) throws IOException, ConfigurationException;
}
