package com.talkweb.ai.indexer.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * JSON配置加载器
 */
public class JsonConfigurationLoader implements ConfigurationLoader {

    private final ObjectMapper objectMapper;

    public JsonConfigurationLoader() {
        this.objectMapper = new ObjectMapper()
                .enable(SerializationFeature.INDENT_OUTPUT)
                .enable(com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_COMMENTS);
    }

    public JsonConfigurationLoader(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper != null ? objectMapper : new ObjectMapper();
    }

    @Override
    public AppConfiguration load(InputStream input) throws IOException, ConfigurationException {
        // 将输入流转换为可重置的字节数组输入流
        byte[] bytes = input.readAllBytes();
        try (ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes)) {
            // 首先尝试解析为Map
            try {
                Map<String, Object> map = objectMapper.readValue(
                        byteArrayInputStream,
                        new TypeReference<Map<String, Object>>() {
                        }
                );
                return new MapConfiguration(map);
            } catch (com.fasterxml.jackson.databind.exc.MismatchedInputException e) {
                // 如果不是Map，尝试解析为List
                byteArrayInputStream.reset();
                List<?> list = objectMapper.readValue(
                        byteArrayInputStream,
                        new TypeReference<List<?>>() {
                        }
                );
                // 将List转换为Map格式
                Map<String, Object> map = new LinkedHashMap<>();
                map.put("_array", list);
                return new MapConfiguration(map);
            }
        } catch (IOException e) {
            throw new ConfigurationException("Failed to load JSON configuration", e);
        }
    }

    /**
     * 将配置写入输出流
     */
    public void save(AppConfiguration config, OutputStream output) throws IOException {
        if (config == null || output == null) {
            throw new IllegalArgumentException("Config and output stream cannot be null");
        }
        objectMapper.writeValue(output, config.toMap());
    }

    /**
     * 将配置转换为JSON字符串
     */
    public String toJson(AppConfiguration config) throws IOException {
        if (config == null) {
            return "{}";
        }
        return objectMapper.writeValueAsString(config.toMap());
    }

    /**
     * 从JSON字符串加载配置
     */
    @Override
    public AppConfiguration fromJson(String json) throws IOException, ConfigurationException {
        try (java.io.ByteArrayInputStream input = new java.io.ByteArrayInputStream(json.getBytes(java.nio.charset.StandardCharsets.UTF_8))) {
            return load(input);
        }
    }

    @Override
    public AppConfiguration fromYaml(String yaml) throws IOException, ConfigurationException {
        throw new UnsupportedOperationException("YAML parsing not supported by JSON loader");
    }
}
