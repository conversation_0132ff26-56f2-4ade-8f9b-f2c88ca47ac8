package com.talkweb.ai.indexer.config;

import java.util.*;

/**
 * 基于Map的配置实现
 */
public class MapConfiguration extends AbstractConfiguration {

    private final Map<String, Object> configMap;

    /**
     * 创建一个空的配置
     */
    public MapConfiguration() {
        this(new LinkedHashMap<>());
    }

    /**
     * 使用指定的Map创建配置
     */
    public MapConfiguration(Map<String, Object> configMap) {
        this.configMap = configMap != null ? new LinkedHashMap<>(configMap) : new LinkedHashMap<>();
    }

    @Override
    public boolean hasKey(String key) {
        if (key == null || key.isEmpty()) {
            return false;
        }
        return getRawValue(key) != null;
    }

    @Override
    public Object getRawValue(String key) {
        if (key == null || key.isEmpty()) {
            return null;
        }

        String[] parts = key.split("\\.");
        Object currentValue = configMap;

        for (int i = 0; i < parts.length; i++) {
            String part = parts[i];
            if (!(currentValue instanceof Map)) {
                return null;
            }

            Object value = ((Map<?, ?>) currentValue).get(part);

            if (value == null) {
                return null;
            }

            if (i == parts.length - 1) {
                return value;
            }

            currentValue = value;
        }

        return null; // Should not be reached if key is valid
    }

    @Override
    public Iterable<String> getKeys() {
        return Collections.unmodifiableSet(configMap.keySet());
    }

    /**
     * 设置配置值
     */
    @SuppressWarnings("unchecked")
    public void set(String key, Object value) {
        if (key == null || key.isEmpty()) {
            return;
        }
        String[] parts = key.split("\\.");
        Map<String, Object> currentMap = this.configMap;
        for (int i = 0; i < parts.length - 1; i++) {
            String part = parts[i];
            Object next = currentMap.get(part);
            if (!(next instanceof Map)) {
                next = new LinkedHashMap<>();
                currentMap.put(part, next);
            }
            currentMap = (Map<String, Object>) next;
        }
        currentMap.put(parts[parts.length - 1], value);
    }

    /**
     * 批量设置配置值
     */
    public void setAll(Map<String, Object> values) {
        if (values != null) {
            configMap.putAll(values);
        }
    }

    /**
     * 移除配置项
     */
    public Object remove(String key) {
        return configMap.remove(key);
    }

    /**
     * 清空所有配置
     */
    public void clear() {
        configMap.clear();
    }

    /**
     * 获取底层的Map
     */
    @Override
    public Map<String, Object> toMap() {
        Map<String, Object> result = new LinkedHashMap<>();
        for (Map.Entry<String, Object> entry : configMap.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof MapConfiguration) {
                result.put(entry.getKey(), ((MapConfiguration) value).toMap());
            } else if (value instanceof List) {
                List<?> list = (List<?>) value;
                List<Object> newList = new ArrayList<>(list.size());
                for (Object item : list) {
                    if (item instanceof MapConfiguration) {
                        newList.add(((MapConfiguration) item).toMap());
                    } else {
                        newList.add(item);
                    }
                }
                result.put(entry.getKey(), newList);
            } else {
                result.put(entry.getKey(), value);
            }
        }
        return result;
    }

    /**
     * 创建一个构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 配置构建器
     */
    @Override
    protected <T> Optional<T> getValue(String key, Class<T> type) {
        Object value = getRawValue(key);
        if (value == null) {
            return Optional.empty();
        }

        if (type.isInstance(value)) {
            return Optional.of(type.cast(value));
        }

        // 尝试类型转换
        if (type == String.class) {
            return Optional.of(type.cast(value.toString()));
        }

        try {
            if (type == Integer.class) {
                return Optional.of(type.cast(Integer.valueOf(value.toString())));
            } else if (type == Long.class) {
                return Optional.of(type.cast(Long.valueOf(value.toString())));
            } else if (type == Double.class) {
                return Optional.of(type.cast(Double.valueOf(value.toString())));
            } else if (type == Boolean.class) {
                String strValue = value.toString().toLowerCase();
                if (strValue.equals("true") || strValue.equals("yes") || strValue.equals("on") || strValue.equals("1")) {
                    return Optional.of(type.cast(Boolean.TRUE));
                } else if (strValue.equals("false") || strValue.equals("no") || strValue.equals("off") || strValue.equals("0")) {
                    return Optional.of(type.cast(Boolean.FALSE));
                }
            }
        } catch (NumberFormatException e) {
            // 转换失败返回空
        }

        return Optional.empty();
    }

    public static class Builder {
        private final Map<String, Object> map = new LinkedHashMap<>();

        @SuppressWarnings("unchecked")
        public Builder put(String key, Object value) {
            if (key == null || key.isEmpty()) {
                return this;
            }
            String[] parts = key.split("\\.");
            Map<String, Object> currentMap = this.map;
            for (int i = 0; i < parts.length - 1; i++) {
                String part = parts[i];
                Object next = currentMap.get(part);
                if (!(next instanceof Map)) {
                    next = new LinkedHashMap<>();
                    currentMap.put(part, next);
                }
                currentMap = (Map<String, Object>) next;
            }
            currentMap.put(parts[parts.length - 1], value);
            return this;
        }

        public Builder putAll(Map<String, Object> values) {
            if (values != null) {
                map.putAll(values);
            }
            return this;
        }

        public MapConfiguration build() {
            return new MapConfiguration(map);
        }
    }
}
