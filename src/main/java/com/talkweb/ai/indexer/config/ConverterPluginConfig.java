
package com.talkweb.ai.indexer.config;

import com.talkweb.ai.indexer.core.impl.*;
import com.talkweb.ai.indexer.core.Plugin;
import com.talkweb.ai.indexer.core.PluginMetadata;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ConverterPluginConfig {

    @Bean
    public Plugin htmlConverterPlugin() {
        return new HtmlToMarkdownConverter(
            PluginMetadata.builder()
                .id("html-converter")
                .name("HTML to Markdown Converter")
                .version("1.0.0")
                .description("Converts HTML/HTML files to Markdown format")
                .build()
        );
    }

    @Bean 
    public Plugin pdfConverterPlugin() {
        return new PdfToMarkdownConverter(
            PluginMetadata.builder()
                .id("pdf-converter")
                .name("PDF to Markdown Converter")
                .version("1.0.0")
                .description("Converts PDF files to Markdown format")
                .build()
        );
    }

    @Bean
    public Plugin excelConverterPlugin() {
        return new ExcelToMarkdownConverter(
            PluginMetadata.builder()
                .id("excel-converter")
                .name("Excel to Markdown Converter")
                .version("1.0.0")
                .description("Converts Excel files to Markdown tables")
                .build()
        );
    }
}
