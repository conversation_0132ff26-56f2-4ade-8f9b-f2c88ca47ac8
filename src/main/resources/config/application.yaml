# Application configuration
doc-converter:
  # General settings
  app:
    name: "Document Converter"
    version: "1.0.0"
    max-concurrent-tasks: 4
    temp-dir: "${TEMP:/tmp/doc-converter}"
  
  # File processing settings
  file:
    # Supported file extensions (without leading .)
    supported-formats:
      - pdf
      - docx
      - doc
      - xlsx
      - pptx
      - html
      - htm
      - txt
      - md
    
    # Default encoding for text files
    default-encoding: "UTF-8"
    
    # File size limits (in bytes)
    max-file-size: 104857600  # 100MB
    
    # Should skip files with unsupported extensions
    skip-unsupported: true
  
  # Output settings
  output:
    # Default output directory (relative to working directory)
    directory: "output"
    
    # Whether to preserve directory structure relative to input
    preserve-structure: true
    
    # File naming pattern (supports {name}, {ext}, {timestamp}, {counter})
    filename-pattern: "{name}.md"
    
    # Whether to overwrite existing files
    overwrite-existing: false
  
  # Logging settings
  logging:
    level: INFO
    file: "logs/application.log"
    max-size: "10MB"
    max-backups: 5
    
    # Format pattern
    pattern: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  
  # Plugin settings
  plugins:
    # Directory containing plugin J<PERSON> files
    directory: "plugins"
    
    # Auto-scan for plugins on startup
    auto-scan: true
    
    # List of plugins to load (empty = load all)
    enabled-plugins:
      - pdf-converter
      - docx-converter
      - excel-converter
      - pptx-converter
      - html-converter
    
    # Plugin-specific configurations
    config:
      pdf-converter:
        extract-images: true
        image-quality: 0.8
        ocr-enabled: true
        ocr-language: "eng+chi_sim"
      
      docx-converter:
        extract-comments: true
        extract-track-changes: false
      
      excel-converter:
        include-sheets: "*"  # Comma-separated list or "*" for all
        include-hidden-sheets: false
        include-formulas: true
      
      pptx-converter:
        extract-speaker-notes: true
        extract-slide-notes: true
        extract-slide-titles: true
      
      html-converter:
        extract-links: true
        extract-images: true
        base-href: ""
  
  # Performance settings
  performance:
    # Thread pool settings
    thread-pool:
      core-size: 4
      max-size: 8
      queue-capacity: 100
      keep-alive-seconds: 60
    
    # Memory management
    memory:
      max-memory-usage: 0.8  # 80% of available heap
      check-interval: 5000    # 5 seconds
  
  # Security settings
  security:
    # File path validation
    validate-file-paths: true
    
    # Prevent directory traversal attacks
    prevent-directory-traversal: true
    
    # Allowed file protocols (file, http, https, etc.)
    allowed-protocols:
      - file
    
    # Maximum number of files to process in a single operation
    max-files-per-operation: 1000

# Spring Boot specific settings
spring:
  application:
    name: doc-converter
  
  # Actuator endpoints for monitoring
  management:
    endpoints:
      web:
        exposure:
          include: health,info,metrics
    endpoint:
      health:
        show-details: when_authorized
      metrics:
        enabled: true
    
    # Health check settings
    health:
      defaults:
        enabled: true
      diskspace:
        enabled: true
      diskSpace:
        path: .
        threshold: 10MB

# Logging configuration (can be overridden by application-logging.yml)
logging:
  level:
    root: INFO
    com.talkweb.ai.indexer: DEBUG
  file:
    name: ${doc-converter.logging.file:logs/application.log}
    max-size: ${doc-converter.logging.max-size:10MB}
  pattern:
    console: "%clr(%d{${LOG_DATEFORMAT_PATTERN:yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"
    file: "%d{${LOG_DATEFORMAT_PATTERN:yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"
