# Plugin configuration example
plugins:
  # PDF Converter Plugin
  pdf-converter:
    enabled: true
    priority: 10
    settings:
      extract-metadata: true
      extract-bookmarks: true
      extract-annotations: true
      extract-attachments: true
      extract-text: true
      extract-images:
        enabled: true
        format: png
        dpi: 150
        quality: 0.9
      ocr:
        enabled: true
        languages:
          - eng
          - chi_sim
        page-segmentation-mode: 3  # 3 = AUTO_OSD
        ocr-engine-mode: 3         # 3 = DEFAULT
      security:
        max-pages: 1000
        allow-javascript: false
        allow-embedded-files: false
  
  # DOCX Converter Plugin
  docx-converter:
    enabled: true
    priority: 20
    settings:
      extract-comments: true
      extract-track-changes: false
      extract-footnotes: true
      extract-endnotes: true
      extract-headers: true
      extract-footers: true
      extract-drawing-shapes: true
      extract-embedded-objects: true
      extract-ole-objects: true
      extract-embedded-packages: true
      extract-hyperlinks: true
      extract-theme: true
      extract-styles: true
      extract-numbering: true
      extract-fonts: true
  
  # Excel Converter Plugin
  excel-converter:
    enabled: true
    priority: 30
    settings:
      include-sheets: "*"  # Comma-separated list or "*" for all
      include-hidden-sheets: false
      include-formulas: true
      include-comments: true
      include-charts: true
      include-pivot-tables: true
      include-formatting: true
      include-cell-styles: true
      include-merged-cells: true
      include-data-validations: true
      include-conditional-formatting: true
  
  # PPTX Converter Plugin
  pptx-converter:
    enabled: true
    priority: 40
    settings:
      extract-slide-notes: true
      extract-slide-titles: true
      extract-slide-numbers: true
      extract-speaker-notes: true
      extract-slide-layout: true
      extract-slide-master: true
      extract-slide-transitions: true
      extract-slide-animations: true
      extract-slide-comments: true
      extract-slide-thumbnails: true
      extract-embedded-files: true
      extract-ole-objects: true
      extract-hyperlinks: true
  
  # HTML Converter Plugin
  html-converter:
    enabled: true
    priority: 50
    settings:
      extract-metadata: true
      extract-text: true
      extract-links: true
      extract-images: true
      extract-tables: true
      extract-forms: true
      extract-comments: true
      extract-scripts: false
      extract-styles: true
      extract-frames: true
      extract-iframes: true
      extract-embedded-objects: true
      extract-meta-refresh: true
      extract-base-href: true
      extract-title: true
      extract-meta-keywords: true
      extract-meta-description: true
      extract-meta-robots: true
      extract-meta-viewport: true
      extract-meta-charset: true
      extract-meta-http-equiv: true
      extract-meta-author: true
      extract-meta-generator: true
      extract-meta-og: true
      extract-meta-twitter: true
      extract-meta-dc: true
      extract-meta-schema: true
      extract-meta-msapplication: true
      extract-meta-apple-itunes-app: true
      extract-meta-format-detection: true
