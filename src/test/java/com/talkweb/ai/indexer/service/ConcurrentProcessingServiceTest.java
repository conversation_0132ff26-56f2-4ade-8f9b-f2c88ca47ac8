package com.talkweb.ai.indexer.service;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

class ConcurrentProcessingServiceTest {

    private ConcurrentProcessingService processingService;

    @BeforeEach
    void setUp() {
        processingService = new ConcurrentProcessingService(4, 100);
    }

    @AfterEach
    void tearDown() {
        processingService.shutdown(1, TimeUnit.SECONDS);
    }

    @Test
    void testProcessItems() throws InterruptedException {
        // Arrange
        List<Integer> items = List.of(1, 2, 3, 4, 5);

        // Act
        List<Integer> results = processingService.processItems(items, (Integer i) -> i * 2);

        // Assert
        assertEquals(5, results.size(), "Result size should match input size");
        assertTrue(results.contains(2), "Results should contain 2");
        assertTrue(results.contains(4), "Results should contain 4");
        assertTrue(results.contains(6), "Results should contain 6");
        assertTrue(results.contains(8), "Results should contain 8");
        assertTrue(results.contains(10), "Results should contain 10");
    }

    @Test
    void testProcessItemsWithEmptyList() throws InterruptedException {
        // Arrange
        List<Integer> items = List.of();

        // Act
        List<Integer> results = processingService.processItems(items, (Integer i) -> i * 2);

        // Assert
        assertTrue(results.isEmpty(), "Result should be empty");
    }

    @Test
    void testProcessItemsWithNullList() throws InterruptedException {
        // Act
        List<Integer> results = processingService.processItems(null, (Integer i) -> i * 2);

        // Assert
        assertTrue(results.isEmpty(), "Result should be empty");
    }

    @Test
    void testConcurrentExecution() throws InterruptedException {
        // Arrange
        int itemCount = 10;
        List<Integer> items = new ArrayList<>();
        for (int i = 0; i < itemCount; i++) {
            items.add(i);
        }

        CountDownLatch startLatch = new CountDownLatch(itemCount);
        CountDownLatch completeLatch = new CountDownLatch(1);
        AtomicInteger concurrentExecutions = new AtomicInteger(0);
        AtomicInteger maxConcurrentExecutions = new AtomicInteger(0);

        // Act
        List<Integer> results = processingService.processItems(items, i -> {
            startLatch.countDown();
            try {
                // Wait for all tasks to start
                startLatch.await(1, TimeUnit.SECONDS);

                // Increment counter and update max
                int current = concurrentExecutions.incrementAndGet();
                int max;
                do {
                    max = maxConcurrentExecutions.get();
                    if (current <= max) break;
                } while (!maxConcurrentExecutions.compareAndSet(max, current));

                // Wait for all tasks to record their execution
                completeLatch.await(1, TimeUnit.SECONDS);

                // Decrement counter
                concurrentExecutions.decrementAndGet();

                return i;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException(e);
            } finally {
                completeLatch.countDown();
            }
        });

        // Assert
        assertEquals(itemCount, results.size(), "All items should be processed");
        assertTrue(maxConcurrentExecutions.get() > 1,
                "Multiple tasks should execute concurrently, got: " + maxConcurrentExecutions.get());
    }

    @Test
    void testProcessItemsWithSpecificConcurrencyLevel() throws InterruptedException {
        // Arrange
        int itemCount = 20;
        int concurrencyLevel = 2;
        List<Integer> items = new ArrayList<>();
        for (int i = 0; i < itemCount; i++) {
            items.add(i);
        }

        CountDownLatch startLatch = new CountDownLatch(concurrencyLevel);
        CountDownLatch proceedLatch = new CountDownLatch(1);
        AtomicInteger concurrentExecutions = new AtomicInteger(0);
        AtomicInteger maxConcurrentExecutions = new AtomicInteger(0);

        // Act - Start processing in a separate thread so we can control the test flow
        Thread processingThread = new Thread(() -> {
            try {
                processingService.processItems(items, i -> {
                    try {
                        int current = concurrentExecutions.incrementAndGet();
                        int max;
                        do {
                            max = maxConcurrentExecutions.get();
                            if (current <= max) break;
                        } while (!maxConcurrentExecutions.compareAndSet(max, current));

                        startLatch.countDown();
                        proceedLatch.await(2, TimeUnit.SECONDS); // Wait for test to check concurrency

                        concurrentExecutions.decrementAndGet();
                        return i;
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException(e);
                    }
                }, concurrencyLevel);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });

        processingThread.start();

        // Wait for tasks to start
        boolean allStarted = startLatch.await(2, TimeUnit.SECONDS);
        assertTrue(allStarted, "Tasks should start within timeout");

        // Assert - Check concurrency level before allowing tasks to complete
        assertEquals(concurrencyLevel, concurrentExecutions.get(),
                "Concurrent executions should match specified concurrency level");
        assertEquals(concurrencyLevel, maxConcurrentExecutions.get(),
                "Max concurrent executions should match specified concurrency level");

        // Allow tasks to complete
        proceedLatch.countDown();
        processingThread.join(5000);
    }

    @Test
    void testShutdownAndTermination() throws InterruptedException {
        // Act
        boolean shutdownResult = processingService.shutdown(1, TimeUnit.SECONDS);

        // Assert
        assertTrue(processingService.isShutdown(), "Service should be shutdown");
        assertTrue(shutdownResult, "Shutdown should complete successfully");
        assertTrue(processingService.isTerminated(), "Service should be terminated");
    }

    @Test
    void testShutdownNow() {
        // Act
        List<Runnable> unfinishedTasks = processingService.shutdownNow();

        // Assert
        assertTrue(processingService.isShutdown(), "Service should be shutdown");
        assertTrue(unfinishedTasks.isEmpty(), "No tasks should be pending");
    }

    @Test
    void testExceptionHandling() {
        // Arrange
        List<Integer> items = List.of(1, 2, 3, 4, 5);

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            processingService.processItems(items, (Integer i) -> {
                if (i == 3) {
                    throw new RuntimeException("Test exception");
                }
                return i * 2;
            });
        }, "Exception in task should propagate to caller");
    }
}
