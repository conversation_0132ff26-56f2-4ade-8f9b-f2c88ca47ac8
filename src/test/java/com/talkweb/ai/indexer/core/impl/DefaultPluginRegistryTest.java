package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.Plugin;
import com.talkweb.ai.indexer.core.PluginContext;
import com.talkweb.ai.indexer.core.PluginException;
import com.talkweb.ai.indexer.core.PluginMetadata;
import com.talkweb.ai.indexer.core.PluginState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Collection;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

class DefaultPluginRegistryTest {

    private DefaultPluginRegistry registry;
    private TestPlugin plugin1;
    private TestPlugin plugin2;
    
    @BeforeEach
    void setUp() {
        registry = new DefaultPluginRegistry();
        plugin1 = new TestPlugin("plugin1", "1.0.0");
        plugin2 = new TestPlugin("plugin2", "1.0.0");
    }
    
    @Test
    void testRegisterAndGetPlugin() throws PluginException {
        // 注册插件
        registry.register(plugin1);
        
        // 验证插件已注册
        assertTrue(registry.hasPlugin("plugin1"));
        assertEquals(1, registry.size());
        
        // 获取插件
        Optional<Plugin> retrievedPlugin = registry.getPlugin("plugin1");
        assertTrue(retrievedPlugin.isPresent());
        assertEquals("plugin1", retrievedPlugin.get().getMetadata().getId());
    }
    
    @Test
    void testRegisterDuplicatePlugin() throws PluginException {
        // 注册插件
        registry.register(plugin1);
        
        // 注册相同ID的插件应该抛出异常
        TestPlugin duplicatePlugin = new TestPlugin("plugin1", "2.0.0");
        assertThrows(PluginException.class, () -> registry.register(duplicatePlugin));
        
        // 验证只有一个插件被注册
        assertEquals(1, registry.size());
    }
    
    @Test
    void testUnregisterPlugin() throws PluginException {
        // 注册插件
        registry.register(plugin1);
        registry.register(plugin2);
        
        // 验证插件已注册
        assertEquals(2, registry.size());
        
        // 取消注册插件
        boolean result = registry.unregister("plugin1");
        
        // 验证结果
        assertTrue(result);
        assertEquals(1, registry.size());
        assertFalse(registry.hasPlugin("plugin1"));
        assertTrue(registry.hasPlugin("plugin2"));
    }
    
    @Test
    void testUnregisterNonExistentPlugin() {
        // 取消注册不存在的插件
        boolean result = registry.unregister("non-existent");
        
        // 验证结果
        assertFalse(result);
    }
    
    @Test
    void testGetAllPlugins() throws PluginException {
        // 注册插件
        registry.register(plugin1);
        registry.register(plugin2);
        
        // 获取所有插件
        Collection<Plugin> plugins = registry.getAllPlugins();
        
        // 验证结果
        assertEquals(2, plugins.size());
        assertTrue(plugins.contains(plugin1));
        assertTrue(plugins.contains(plugin2));
    }
    
    @Test
    void testGetPluginsByType() throws PluginException {
        // 注册插件
        registry.register(plugin1);
        registry.register(plugin2);
        
        // 获取指定类型的插件
        Collection<TestPlugin> plugins = registry.getPluginsByType(TestPlugin.class);
        
        // 验证结果
        assertEquals(2, plugins.size());
        assertTrue(plugins.contains(plugin1));
        assertTrue(plugins.contains(plugin2));
    }
    
    @Test
    void testGetPluginByTypeAndId() throws PluginException {
        // 注册插件
        registry.register(plugin1);
        registry.register(plugin2);
        
        // 获取指定类型和ID的插件
        Optional<TestPlugin> plugin = registry.getPlugin(TestPlugin.class, "plugin1");
        
        // 验证结果
        assertTrue(plugin.isPresent());
        assertEquals("plugin1", plugin.get().getMetadata().getId());
    }
    
    @Test
    void testClear() throws PluginException {
        // 注册插件
        registry.register(plugin1);
        registry.register(plugin2);
        
        // 清空注册表
        registry.clear();
        
        // 验证结果
        assertEquals(0, registry.size());
        assertFalse(registry.hasPlugin("plugin1"));
        assertFalse(registry.hasPlugin("plugin2"));
    }
    
    // 测试插件类
    private static class TestPlugin implements Plugin {
        private final PluginMetadata metadata;
        private PluginState state = PluginState.REGISTERED;
        
        public TestPlugin(String id, String version) {
            this.metadata = new PluginMetadata(id, id, version, "Test Plugin", "Test", getClass().getName());
        }
        
        @Override
        public PluginMetadata getMetadata() {
            return metadata;
        }
        
        @Override
        public PluginState getState() {
            return state;
        }
        
        @Override
        public void init(PluginContext context) {
            // 测试初始化
            state = PluginState.READY;
        }
        
        @Override
        public void start() {
            // 测试启动
            state = PluginState.RUNNING;
        }
        
        @Override
        public void stop() {
            // 测试停止
            state = PluginState.STOPPED;
        }
        
        @Override
        public void destroy() {
            // 测试销毁
            state = PluginState.DESTROYED;
        }
    }
}