package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.Plugin;
import com.talkweb.ai.indexer.core.PluginContext;
import com.talkweb.ai.indexer.core.PluginException;
import com.talkweb.ai.indexer.core.PluginState;
import com.talkweb.ai.indexer.core.PluginMetadata;

public class TestPlugin implements Plugin {
    private PluginState state = PluginState.LOADED;
    private final PluginMetadata metadata;

    public TestPlugin() {
        this.metadata = new PluginMetadata("lifecycle-plugin", "Test Plugin", "1.0.0", "A test plugin", "Test Author", "com.talkweb.ai.indexer.core.impl.TestPlugin");
    }

    public TestPlugin(String pluginId) {
        this.metadata = new PluginMetadata(pluginId, "Test Plugin", "1.0.0", "A test plugin", "Test Author", "com.talkweb.ai.indexer.core.impl.TestPlugin");
    }

    public TestPlugin(PluginMetadata metadata) {
        this.metadata = metadata;
    }

    @Override
    public PluginMetadata getMetadata() {
        return metadata;
    }

    @Override
    public PluginState getState() {
        return state;
    }

    @Override
    public void init(PluginContext context) throws PluginException {
        this.state = PluginState.READY;
    }

    @Override
    public void start() throws PluginException {
        this.state = PluginState.RUNNING;
    }

    @Override
    public void stop() throws PluginException {
        this.state = PluginState.STOPPED;
    }

    @Override
    public void destroy() throws PluginException {
        this.state = PluginState.DESTROYED;
    }
}
