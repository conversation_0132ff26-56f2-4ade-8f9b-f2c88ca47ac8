package com.talkweb.ai.indexer.core;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

class ProcessingContextTest {

    @TempDir
    Path tempDir;
    
    @Test
    void testBuilderAndGetters() {
        // 创建上下文
        ProcessingContext context = new ProcessingContext.Builder()
                .setOutputPath(tempDir)
                .setForce(true)
                .setPreserveImages(false)
                .setExtractMetadata(true)
                .setExtractImages(false)
                .setExtractTables(true)
                .attribute("customKey", "customValue")
                .build();
        
        // 验证属性
        assertEquals(tempDir, context.getOutputPath());
        assertTrue(context.isForce());
        assertFalse(context.isPreserveImages());
        assertTrue(context.isExtractMetadata());
        assertFalse(context.isExtractImages());
        assertTrue(context.isExtractTables());
        assertEquals("customValue", context.getAttribute("customKey"));
    }
    
    @Test
    void testDefaultValues() {
        // 创建上下文，不设置任何属性
        ProcessingContext context = new ProcessingContext.Builder().build();
        
        // 验证默认值
        assertNull(context.getOutputPath());
        assertFalse(context.isForce());
        assertTrue(context.isPreserveImages());
        assertTrue(context.isExtractMetadata());
        assertTrue(context.isExtractImages());
        assertTrue(context.isExtractTables());
    }
    
    @Test
    void testGetAttributeWithDefault() {
        // 创建上下文
        ProcessingContext context = new ProcessingContext.Builder()
                .attribute("key1", "value1")
                .build();
        
        // 测试存在的属性
        assertEquals("value1", context.getAttribute("key1", "default"));
        
        // 测试不存在的属性
        assertEquals("default", context.getAttribute("key2", "default"));
    }
    
    @Test
    void testGetBooleanAttribute() {
        // 创建上下文
        ProcessingContext context = new ProcessingContext.Builder()
                .attribute("boolTrue", true)
                .attribute("boolFalse", false)
                .attribute("notBool", "string")
                .build();
        
        // 测试布尔属性
        assertTrue(context.getBooleanAttribute("boolTrue", false));
        assertFalse(context.getBooleanAttribute("boolFalse", true));
        
        // 测试非布尔属性
        assertTrue(context.getBooleanAttribute("notBool", true));
        
        // 测试不存在的属性
        assertTrue(context.getBooleanAttribute("nonExistent", true));
        assertFalse(context.getBooleanAttribute("nonExistent", false));
    }
    
    @Test
    void testGetStringAttribute() {
        // 创建上下文
        ProcessingContext context = new ProcessingContext.Builder()
                .attribute("string", "value")
                .attribute("number", 123)
                .build();
        
        // 测试字符串属性
        assertEquals("value", context.getStringAttribute("string", "default"));
        
        // 测试非字符串属性
        assertEquals("123", context.getStringAttribute("number", "default"));
        
        // 测试不存在的属性
        assertEquals("default", context.getStringAttribute("nonExistent", "default"));
    }
}