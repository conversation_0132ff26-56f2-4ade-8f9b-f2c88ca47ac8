package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.PluginConfig;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;

import java.nio.file.Path;
import java.nio.file.Paths;

public abstract class PluginTestBase {
    protected Path pluginsDir;
    protected Path tempDir;
    protected DefaultPluginManager pluginManager;

    protected PluginConfig config;
    @BeforeEach
    public void setUp() throws Exception {
        pluginsDir = Paths.get("src/test/resources/plugins");
        tempDir = Paths.get("target/test-temp");
        config = new MockPluginConfig("src/test/resources/plugins");
        pluginManager = new DefaultPluginManager(config);
        pluginManager.start();
    }

    @AfterEach
    public void tearDown() throws Exception {
        pluginManager.shutdown();
    }
}
