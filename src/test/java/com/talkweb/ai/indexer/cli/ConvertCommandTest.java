package com.talkweb.ai.indexer.cli;

import com.talkweb.ai.indexer.core.*;
import com.talkweb.ai.indexer.service.FileScannerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import picocli.CommandLine;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class ConvertCommandTest {

    @TempDir
    Path tempDir;
    
    @Mock
    private PluginManager pluginManager;
    
    @Mock
    private FileScannerService fileScannerService;
    
    @Mock
    private DocumentProcessor documentProcessor;
    
    @Mock
    private DocConverterCommand parentCommand;
    
    private ConvertCommand convertCommand;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 设置父命令
        when(parentCommand.isVerbose()).thenReturn(false);
        
        // 设置文档处理器
        PluginMetadata metadata = new PluginMetadata("test-plugin", "Test Plugin", "1.0.0", "Test plugin", "Test", "TestPlugin");
        when(documentProcessor.getMetadata()).thenReturn(metadata);
        when(pluginManager.getPlugins()).thenReturn(Collections.singletonList(documentProcessor));
        when(documentProcessor.supports(anyString())).thenReturn(true);
        
        // 创建命令
        convertCommand = new ConvertCommand(pluginManager, fileScannerService);
        // 设置父命令
        convertCommand.setParentCommand(parentCommand);
        
        CommandLine cmd = new CommandLine(convertCommand);
        cmd.setExecutionExceptionHandler((ex, cmdLine, parseResult) -> {
            ex.printStackTrace();
            return 1;
        });
    }
    
    @Test
    void testConvertSingleFile() throws Exception {
        // 创建测试文件
        Path testFile = tempDir.resolve("test.txt");
        Files.writeString(testFile, "Test content");
        
        // 设置输出目录
        Path outputDir = tempDir.resolve("output");
        
        // 设置文件扫描器
        when(fileScannerService.shouldProcessFile(eq(testFile), any(), any())).thenReturn(true);
        
        // 设置文档处理器
        ProcessingResult result = ProcessingResult.success(new File(outputDir.toFile(), "test.md"));
        when(documentProcessor.process(any(), any())).thenReturn(result);
        
        // 执行命令
        String[] args = {
            "-i", testFile.toString(),
            "-o", outputDir.toString()
        };
        
        int exitCode = new CommandLine(convertCommand).execute(args);
        
        // 验证结果
        assertEquals(0, exitCode);
    }
    
    @Test
    void testConvertDirectory() throws Exception {
        // 创建测试目录和文件
        Path testDir = tempDir.resolve("testDir");
        Files.createDirectories(testDir);
        
        Path testFile1 = testDir.resolve("test1.txt");
        Path testFile2 = testDir.resolve("test2.txt");
        Files.writeString(testFile1, "Test content 1");
        Files.writeString(testFile2, "Test content 2");
        
        // 设置输出目录
        Path outputDir = tempDir.resolve("output");
        
        // 设置文件扫描器
        List<Path> filesToProcess = Arrays.asList(testFile1, testFile2);
        when(fileScannerService.scan(eq(testDir), anyBoolean(), any(), any())).thenReturn(filesToProcess);
        
        // 设置文档处理器
        ProcessingResult result1 = ProcessingResult.success(new File(outputDir.toFile(), "test1.md"));
        ProcessingResult result2 = ProcessingResult.success(new File(outputDir.toFile(), "test2.md"));
        when(documentProcessor.process(eq(testFile1.toFile()), any())).thenReturn(result1);
        when(documentProcessor.process(eq(testFile2.toFile()), any())).thenReturn(result2);
        
        // 执行命令
        String[] args = {
            "-i", testDir.toString(),
            "-o", outputDir.toString(),
            "-r"
        };
        
        int exitCode = new CommandLine(convertCommand).execute(args);
        
        // 验证结果
        assertEquals(0, exitCode);
    }
}