package com.talkweb.ai.indexer.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class JsonConfigurationLoaderTest {

    private JsonConfigurationLoader loader;

    @BeforeEach
    void setUp() {
        loader = new JsonConfigurationLoader();
    }

    @Test
    void testLoadFromString() throws IOException, ConfigurationException {
        String json = """
        {
            "app": {
                "name": "Test App",
                "version": "1.0.0",
                "enabled": true,
                "count": 42,
                "pi": 3.14159,
                "tags": ["one", "two", "three"]
            },
            "nested": {
                "value": "test",
                "enabled": false
            }
        }
        """;

        AppConfiguration config;
        try (InputStream input = new ByteArrayInputStream(json.getBytes(StandardCharsets.UTF_8))) {
            config = loader.load(input);
        }

        assertEquals("Test App", config.getString("app.name", null));
        assertEquals("1.0.0", config.getString("app.version", null));
        assertTrue(config.getBoolean("app.enabled", false));
        assertEquals(42, config.getInt("app.count", 0));
        assertEquals(3.14159, config.getDouble("app.pi", 0.0), 0.00001);

        List<String> tags = config.getStringList("app.tags");
        assertEquals(3, tags.size());
        assertEquals("one", tags.get(0));
        assertEquals("two", tags.get(1));
        assertEquals("three", tags.get(2));

        AppConfiguration nested = config.getConfig("nested").orElseThrow();
        assertEquals("test", nested.getString("value", null));
        assertFalse(nested.getBoolean("enabled", true));
    }

    @Test
    void testLoadFromFile(@TempDir Path tempDir) throws IOException, ConfigurationException {
        Path jsonFile = tempDir.resolve("config.json");
        String json = """
        {
            "database": {
                "host": "localhost",
                "port": 5432,
                "name": "testdb",
                "users": ["admin", "user1", "user2"]
            }
        }
        """;

        Files.writeString(jsonFile, json, StandardCharsets.UTF_8);

        AppConfiguration config;
        try (InputStream input = Files.newInputStream(jsonFile)) {
            config = loader.load(input);
        }

        AppConfiguration dbConfig = config.getConfig("database").orElseThrow();
        assertEquals("localhost", dbConfig.getString("host", null));
        assertEquals(5432, dbConfig.getInt("port", 0));

        List<String> users = dbConfig.getStringList("users");
        assertEquals(3, users.size());
        assertEquals("admin", users.get(0));
        assertEquals("user1", users.get(1));
        assertEquals("user2", users.get(2));
    }

    @Test
    void testLoadWithComments() throws IOException, ConfigurationException {
        // JSON doesn't support comments, but some parsers do
        String json = """
        // This is a comment
        {
            // Another comment
            "name": "Test",
            "value": 123,
            /* Multi-line
               comment */
            "enabled": true
        }
        """;

        AppConfiguration config;
        try (InputStream input = new ByteArrayInputStream(json.getBytes(StandardCharsets.UTF_8))) {
            config = loader.load(input);
        }

        assertEquals("Test", config.getString("name", null));
        assertEquals(123, config.getInt("value", 0));
        assertTrue(config.getBoolean("enabled", false));
    }

    @Test
    void testLoadInvalidJson() {
        String invalidJson = "{name: 'test'}"; // Missing quotes around key

        assertThrows(ConfigurationException.class, () -> {
            try (InputStream input = new ByteArrayInputStream(invalidJson.getBytes(StandardCharsets.UTF_8))) {
                loader.load(input);
            }
        });
    }

    @Test
    void testLoadEmptyJson() throws IOException, ConfigurationException {
        String emptyJson = "{}";
        AppConfiguration config;
        try (InputStream input = new ByteArrayInputStream(emptyJson.getBytes(StandardCharsets.UTF_8))) {
            config = loader.load(input);
        }
        assertFalse(config.getKeys().iterator().hasNext());
    }

    @Test
    void testLoadArrayRoot() throws IOException, ConfigurationException {
        String arrayJson = "[{\"name\":\"one\"},{\"name\":\"two\"}]";
        AppConfiguration config;
        try (InputStream input = new ByteArrayInputStream(arrayJson.getBytes(StandardCharsets.UTF_8))) {
            config = loader.load(input);
        }

        // Should be converted to a config with a single key "_array" containing the array
        boolean hasArrayKey = false;
        for (String key : config.getKeys()) {
            if (key.equals("_array")) {
                hasArrayKey = true;
                break;
            }
        }
        assertTrue(hasArrayKey);
        List<AppConfiguration> items = config.getConfigList("_array");
        assertEquals(2, items.size());
        assertEquals("one", items.get(0).getString("name", ""));
        assertEquals("two", items.get(1).getString("name", ""));
    }

    @Test
    void testSaveAndReload(@TempDir Path tempDir) throws IOException, ConfigurationException {
        // Create initial config
        MapConfiguration config = new MapConfiguration();
        config.set("app.name", "Test App");
        config.set("app.version", "1.0.0");
        config.set("app.enabled", true);
        config.set("app.tags", List.of("tag1", "tag2"));

        // Save to file
        Path jsonFile = tempDir.resolve("output.json");
        try (OutputStream output = Files.newOutputStream(jsonFile)) {
            loader.save(config, output);
        }

        // Reload
        AppConfiguration loaded;
        try (InputStream input = Files.newInputStream(jsonFile)) {
            loaded = loader.load(input);
        }

        // Verify
        assertEquals("Test App", loaded.getString("app.name", null));
        assertEquals("1.0.0", loaded.getString("app.version", null));
        assertTrue(loaded.getBoolean("app.enabled", false));

        List<String> tags = loaded.getStringList("app.tags");
        assertEquals(2, tags.size());
        assertEquals("tag1", tags.get(0));
        assertEquals("tag2", tags.get(1));
    }

    @Test
    void testSaveAndReloadNested(@TempDir Path tempDir) throws IOException, ConfigurationException {
        // Create nested config
        MapConfiguration nested = new MapConfiguration();
        nested.set("host", "localhost");
        nested.set("port", 5432);

        MapConfiguration config = new MapConfiguration();
        config.set("database", nested);
        config.set("enabled", true);

        // Save and reload
        Path jsonFile = tempDir.resolve("nested.json");
        try (OutputStream output = Files.newOutputStream(jsonFile)) {
            loader.save(config, output);
        }
        AppConfiguration loaded;
        try (InputStream input = Files.newInputStream(jsonFile)) {
            loaded = loader.load(input);
        }

        // Verify
        assertTrue(loaded.getBoolean("enabled", false));

        AppConfiguration loadedDb = loaded.getConfig("database").orElseThrow();
        assertEquals("localhost", loadedDb.getString("host", null));
        assertEquals(5432, loadedDb.getInt("port", 0));
    }
}
