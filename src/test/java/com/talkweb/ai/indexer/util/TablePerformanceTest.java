package com.talkweb.ai.indexer.util;

import com.talkweb.ai.indexer.util.HtmlConversionMode;
import com.talkweb.ai.indexer.util.markdown.converter.TableConverter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Performance tests for the enhanced table converter
 */
public class TablePerformanceTest {

    @BeforeEach
    void setUp() {
        // Clear caches before each test
        TableConverter.clearCaches();
    }

    @AfterEach
    void tearDown() {
        // Clear caches after each test
        TableConverter.clearCaches();
    }

    @Test
    void testFrameworkDetectionCaching() {
        String bootstrapTableHtml = """
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Age</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>John</td>
                            <td>25</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            """;

        // First conversion - should populate cache
        long startTime = System.nanoTime();
        String result1 = HtmlToMarkdownConverter.convert(bootstrapTableHtml, HtmlConversionMode.LOOSE);
        long firstConversionTime = System.nanoTime() - startTime;

        // Second conversion - should use cache
        startTime = System.nanoTime();
        String result2 = HtmlToMarkdownConverter.convert(bootstrapTableHtml, HtmlConversionMode.LOOSE);
        long secondConversionTime = System.nanoTime() - startTime;

        // Verify results are identical
        assertEquals(result1, result2);
        assertTrue(result1.contains("| Name | Age |"));

        // Check cache statistics
        Map<String, Integer> stats = TableConverter.getCacheStatistics();
        assertTrue(stats.get("frameworkCacheSize") > 0, "Framework cache should have entries");

        System.out.println("First conversion: " + (firstConversionTime / 1_000_000.0) + "ms");
        System.out.println("Second conversion: " + (secondConversionTime / 1_000_000.0) + "ms");
        System.out.println("Cache stats: " + stats);
    }

    @Test
    void testComponentTextCaching() {
        String antDesignTableHtml = """
            <div class="ant-table-wrapper">
                <table class="ant-table">
                    <thead>
                        <tr>
                            <th class="ant-table-cell">Name</th>
                            <th class="ant-table-cell">Tags</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="ant-table-row">
                            <td class="ant-table-cell">John</td>
                            <td class="ant-table-cell">
                                <span class="ant-tag ant-tag-blue">Developer</span>
                                <span class="ant-tag ant-tag-green">Nice</span>
                            </td>
                        </tr>
                        <tr class="ant-table-row">
                            <td class="ant-table-cell">Jane</td>
                            <td class="ant-table-cell">
                                <span class="ant-tag ant-tag-blue">Developer</span>
                                <span class="ant-tag ant-tag-red">Expert</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            """;

        // Convert table with repeated components
        String result = HtmlToMarkdownConverter.convert(antDesignTableHtml, HtmlConversionMode.LOOSE);

        // Verify conversion worked
        assertTrue(result.contains("| Name | Tags |"));
        assertTrue(result.contains("| John | Developer Nice |"));
        assertTrue(result.contains("| Jane | Developer Expert |"));

        // Check cache statistics
        Map<String, Integer> stats = TableConverter.getCacheStatistics();
        assertTrue(stats.get("componentCacheSize") > 0, "Component cache should have entries");

        System.out.println("Component cache stats: " + stats);
    }

    @Test
    void testMemoryOptimizationWithLargeTables() {
        // Create a large table with many components
        StringBuilder largeTableHtml = new StringBuilder();
        largeTableHtml.append("""
            <div class="ant-table-wrapper">
                <table class="ant-table">
                    <thead>
                        <tr>
                            <th class="ant-table-cell">ID</th>
                            <th class="ant-table-cell">Name</th>
                            <th class="ant-table-cell">Status</th>
                            <th class="ant-table-cell">Tags</th>
                        </tr>
                    </thead>
                    <tbody>
            """);

        // Add 500 rows with components
        for (int i = 1; i <= 500; i++) {
            largeTableHtml.append(String.format("""
                        <tr class="ant-table-row">
                            <td class="ant-table-cell">%d</td>
                            <td class="ant-table-cell">User %d</td>
                            <td class="ant-table-cell">
                                <span class="ant-tag ant-tag-green">Active</span>
                            </td>
                            <td class="ant-table-cell">
                                <span class="ant-tag ant-tag-blue">Developer</span>
                                <span class="ant-tag ant-tag-orange">Nice</span>
                            </td>
                        </tr>
                """, i, i));
        }

        largeTableHtml.append("""
                    </tbody>
                </table>
            </div>
            """);

        // Measure memory usage and conversion time
        Runtime runtime = Runtime.getRuntime();
        long memoryBefore = runtime.totalMemory() - runtime.freeMemory();

        long startTime = System.nanoTime();
        String result = HtmlToMarkdownConverter.convert(largeTableHtml.toString(), HtmlConversionMode.LOOSE);
        long conversionTime = System.nanoTime() - startTime;

        long memoryAfter = runtime.totalMemory() - runtime.freeMemory();

        // Verify the result
        assertTrue(result.contains("| ID | Name | Status | Tags |"));
        assertTrue(result.contains("| 1 | User 1 | Active | Developer Nice |"));
        assertTrue(result.contains("| 500 | User 500 | Active | Developer Nice |"));

        // Performance assertions
        double conversionTimeMs = conversionTime / 1_000_000.0;
        assertTrue(conversionTimeMs < 2000, "Large table conversion should take less than 2 seconds, took: " + conversionTimeMs + "ms");

        long memoryUsed = memoryAfter - memoryBefore;
        System.out.println("Memory used: " + (memoryUsed / 1024 / 1024) + " MB");
        System.out.println("Conversion time: " + conversionTimeMs + "ms");

        // Check cache statistics
        Map<String, Integer> stats = TableConverter.getCacheStatistics();
        System.out.println("Final cache stats: " + stats);
    }

    @Test
    void testCacheLimits() {
        // Test that caches don't grow beyond their limits
        Map<String, Integer> initialStats = TableConverter.getCacheStatistics();
        int initialFrameworkCacheSize = initialStats.get("frameworkCacheSize");
        int initialComponentCacheSize = initialStats.get("componentCacheSize");

        // Create many different table structures to test cache limits
        for (int i = 0; i < 1200; i++) { // More than MAX_CACHE_SIZE
            String uniqueTableHtml = String.format("""
                <div class="table-wrapper-%d">
                    <table class="table custom-table-%d">
                        <tr>
                            <th>Header %d</th>
                        </tr>
                        <tr>
                            <td>Data %d</td>
                        </tr>
                    </table>
                </div>
                """, i, i, i, i);

            HtmlToMarkdownConverter.convert(uniqueTableHtml, HtmlConversionMode.LOOSE);
        }

        Map<String, Integer> finalStats = TableConverter.getCacheStatistics();
        int finalFrameworkCacheSize = finalStats.get("frameworkCacheSize");
        int finalComponentCacheSize = finalStats.get("componentCacheSize");

        // Verify cache sizes are within limits
        assertTrue(finalFrameworkCacheSize <= finalStats.get("frameworkCacheLimit"),
                "Framework cache should not exceed limit");
        assertTrue(finalComponentCacheSize <= finalStats.get("componentCacheLimit"),
                "Component cache should not exceed limit");

        System.out.println("Initial framework cache: " + initialFrameworkCacheSize);
        System.out.println("Final framework cache: " + finalFrameworkCacheSize);
        System.out.println("Initial component cache: " + initialComponentCacheSize);
        System.out.println("Final component cache: " + finalComponentCacheSize);
    }

    @Test
    void testCacheClearance() {
        // Populate caches
        String tableHtml = """
            <table class="table table-striped">
                <tr>
                    <th>Test</th>
                </tr>
                <tr>
                    <td><span class="badge">Active</span></td>
                </tr>
            </table>
            """;

        HtmlToMarkdownConverter.convert(tableHtml, HtmlConversionMode.LOOSE);

        Map<String, Integer> statsBeforeClear = TableConverter.getCacheStatistics();
        assertTrue(statsBeforeClear.get("frameworkCacheSize") > 0 || 
                  statsBeforeClear.get("componentCacheSize") > 0,
                  "At least one cache should have entries");

        // Clear caches
        TableConverter.clearCaches();

        Map<String, Integer> statsAfterClear = TableConverter.getCacheStatistics();
        assertEquals(0, statsAfterClear.get("frameworkCacheSize"), "Framework cache should be empty after clear");
        assertEquals(0, statsAfterClear.get("componentCacheSize"), "Component cache should be empty after clear");
    }
}
