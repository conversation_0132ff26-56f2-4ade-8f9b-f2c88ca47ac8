package com.talkweb.ai.indexer.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for enhanced table processing with framework compatibility
 */
class EnhancedTableConverterTest {

    @Test
    void testBootstrapTable() {
        String bootstrapTableHtml = """
            <div class="table-responsive">
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><PERSON></td>
                            <td><span class="badge badge-success">Active</span></td>
                            <td><button class="btn btn-primary">Edit</button></td>
                        </tr>
                        <tr>
                            <td><PERSON></td>
                            <td><span class="badge badge-warning">Pending</span></td>
                            <td><button class="btn btn-secondary">View</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            """;
        
        String result = HtmlToMarkdownConverter.convert(bootstrapTableHtml, HtmlConversionMode.LOOSE);
        
        // Verify table structure
        assertTrue(result.contains("| Name | Status | Actions |"));
        assertTrue(result.contains("|---|---|---|"));
        assertTrue(result.contains("| John Doe | Active | Edit |"));
        assertTrue(result.contains("| Jane Smith | Pending | View |"));
    }

    @Test
    void testTailwindTable() {
        String tailwindTableHtml = """
            <div class="overflow-x-auto">
                <table class="table-auto w-full border-collapse border border-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="border border-gray-300 px-4 py-2">Product</th>
                            <th class="border border-gray-300 px-4 py-2">Price</th>
                            <th class="border border-gray-300 px-4 py-2">Stock</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="hover:bg-gray-100">
                            <td class="border border-gray-300 px-4 py-2">Laptop</td>
                            <td class="border border-gray-300 px-4 py-2">$999</td>
                            <td class="border border-gray-300 px-4 py-2">15</td>
                        </tr>
                        <tr class="hover:bg-gray-100">
                            <td class="border border-gray-300 px-4 py-2">Mouse</td>
                            <td class="border border-gray-300 px-4 py-2">$25</td>
                            <td class="border border-gray-300 px-4 py-2">50</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            """;
        
        String result = HtmlToMarkdownConverter.convert(tailwindTableHtml, HtmlConversionMode.LOOSE);
        
        // Verify table structure
        assertTrue(result.contains("| Product | Price | Stock |"));
        assertTrue(result.contains("|---|---|---|"));
        assertTrue(result.contains("| Laptop | $999 | 15 |"));
        assertTrue(result.contains("| Mouse | $25 | 50 |"));
    }

    @Test
    void testDataTable() {
        String dataTableHtml = """
            <div class="dataTables_wrapper">
                <table id="example" class="display dataTable" style="width:100%">
                    <thead>
                        <tr>
                            <th class="sorting">Name</th>
                            <th class="sorting">Position</th>
                            <th class="sorting">Office</th>
                            <th class="sorting">Salary</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Tiger Nixon</td>
                            <td>System Architect</td>
                            <td>Edinburgh</td>
                            <td>$320,800</td>
                        </tr>
                        <tr>
                            <td>Garrett Winters</td>
                            <td>Accountant</td>
                            <td>Tokyo</td>
                            <td>$170,750</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            """;
        
        String result = HtmlToMarkdownConverter.convert(dataTableHtml, HtmlConversionMode.LOOSE);
        
        // Verify table structure
        assertTrue(result.contains("| Name | Position | Office | Salary |"));
        assertTrue(result.contains("|---|---|---|---|"));
        assertTrue(result.contains("| Tiger Nixon | System Architect | Edinburgh | $320,800 |"));
        assertTrue(result.contains("| Garrett Winters | Accountant | Tokyo | $170,750 |"));
    }

    @Test
    void testMaterialDesignTable() {
        String materialTableHtml = """
            <div class="mdc-data-table">
                <div class="mdc-data-table__table-container">
                    <table class="mdc-data-table__table">
                        <thead>
                            <tr class="mdc-data-table__header-row">
                                <th class="mdc-data-table__header-cell">Dessert</th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--numeric">Calories</th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--numeric">Fat (g)</th>
                            </tr>
                        </thead>
                        <tbody class="mdc-data-table__content">
                            <tr class="mdc-data-table__row">
                                <td class="mdc-data-table__cell">Frozen yogurt</td>
                                <td class="mdc-data-table__cell mdc-data-table__cell--numeric">159</td>
                                <td class="mdc-data-table__cell mdc-data-table__cell--numeric">6.0</td>
                            </tr>
                            <tr class="mdc-data-table__row">
                                <td class="mdc-data-table__cell">Ice cream sandwich</td>
                                <td class="mdc-data-table__cell mdc-data-table__cell--numeric">237</td>
                                <td class="mdc-data-table__cell mdc-data-table__cell--numeric">9.0</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            """;
        
        String result = HtmlToMarkdownConverter.convert(materialTableHtml, HtmlConversionMode.LOOSE);
        
        // Verify table structure
        assertTrue(result.contains("| Dessert | Calories | Fat (g) |"));
        assertTrue(result.contains("|---|---|---|"));
        assertTrue(result.contains("| Frozen yogurt | 159 | 6.0 |"));
        assertTrue(result.contains("| Ice cream sandwich | 237 | 9.0 |"));
    }

    @Test
    void testFoundationTable() {
        String foundationTableHtml = """
            <table class="table hover">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Instrument</th>
                        <th>Favorite Color</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Jimi Hendrix</td>
                        <td>Guitar</td>
                        <td>Purple</td>
                    </tr>
                    <tr>
                        <td>Miles Davis</td>
                        <td>Trumpet</td>
                        <td>Blue</td>
                    </tr>
                </tbody>
            </table>
            """;
        
        String result = HtmlToMarkdownConverter.convert(foundationTableHtml, HtmlConversionMode.LOOSE);
        
        // Verify table structure
        assertTrue(result.contains("| Name | Instrument | Favorite Color |"));
        assertTrue(result.contains("|---|---|---|"));
        assertTrue(result.contains("| Jimi Hendrix | Guitar | Purple |"));
        assertTrue(result.contains("| Miles Davis | Trumpet | Blue |"));
    }

    @Test
    void testSemanticUITable() {
        String semanticTableHtml = """
            <table class="ui celled striped table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Status</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>John</td>
                        <td class="positive">Approved</td>
                        <td>None</td>
                    </tr>
                    <tr>
                        <td>Jamie</td>
                        <td class="negative">Denied</td>
                        <td>Requires call</td>
                    </tr>
                </tbody>
            </table>
            """;

        String result = HtmlToMarkdownConverter.convert(semanticTableHtml, HtmlConversionMode.LOOSE);

        // Verify table structure
        assertTrue(result.contains("| Name | Status | Notes |"));
        assertTrue(result.contains("|---|---|---|"));
        assertTrue(result.contains("| John | Approved | None |"));
        assertTrue(result.contains("| Jamie | Denied | Requires call |"));
    }

    @Test
    void testAntDesignTable() {
        String antDesignTableHtml = """
            <div class="ant-table-wrapper">
                <div class="ant-spin-nested-loading">
                    <div class="ant-spin-container">
                        <div class="ant-table">
                            <div class="ant-table-container">
                                <div class="ant-table-content">
                                    <table>
                                        <thead class="ant-table-thead">
                                            <tr>
                                                <th class="ant-table-cell">Name</th>
                                                <th class="ant-table-cell">Age</th>
                                                <th class="ant-table-cell">Status</th>
                                                <th class="ant-table-cell">Tags</th>
                                                <th class="ant-table-cell">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody class="ant-table-tbody">
                                            <tr class="ant-table-row">
                                                <td class="ant-table-cell">John Brown</td>
                                                <td class="ant-table-cell">32</td>
                                                <td class="ant-table-cell">
                                                    <span class="ant-badge ant-badge-status ant-badge-not-a-wrapper">
                                                        <span class="ant-badge-status-dot ant-badge-status-success"></span>
                                                        <span class="ant-badge-status-text">Active</span>
                                                    </span>
                                                </td>
                                                <td class="ant-table-cell">
                                                    <span class="ant-tag ant-tag-blue">Developer</span>
                                                    <span class="ant-tag ant-tag-green">Nice</span>
                                                </td>
                                                <td class="ant-table-cell">
                                                    <button class="ant-btn ant-btn-primary ant-btn-sm">Edit</button>
                                                    <button class="ant-btn ant-btn-danger ant-btn-sm">Delete</button>
                                                </td>
                                            </tr>
                                            <tr class="ant-table-row">
                                                <td class="ant-table-cell">Jim Green</td>
                                                <td class="ant-table-cell">42</td>
                                                <td class="ant-table-cell">
                                                    <span class="ant-badge ant-badge-status ant-badge-not-a-wrapper">
                                                        <span class="ant-badge-status-dot ant-badge-status-warning"></span>
                                                        <span class="ant-badge-status-text">Pending</span>
                                                    </span>
                                                </td>
                                                <td class="ant-table-cell">
                                                    <span class="ant-tag ant-tag-red">Loser</span>
                                                </td>
                                                <td class="ant-table-cell">
                                                    <button class="ant-btn ant-btn-primary ant-btn-sm">Edit</button>
                                                    <button class="ant-btn ant-btn-danger ant-btn-sm">Delete</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            """;

        String result = HtmlToMarkdownConverter.convert(antDesignTableHtml, HtmlConversionMode.LOOSE);

        // Verify table structure
        assertTrue(result.contains("| Name | Age | Status | Tags | Action |"));
        assertTrue(result.contains("|---|---|---|---|---|"));
        assertTrue(result.contains("| John Brown | 32 | Active | Developer Nice | Edit Delete |"));
        assertTrue(result.contains("| Jim Green | 42 | Pending | Loser | Edit Delete |"));
    }

    @Test
    void testAntDesignTableWithSelection() {
        String antDesignSelectionTableHtml = """
            <div class="ant-table-wrapper">
                <div class="ant-table">
                    <div class="ant-table-container">
                        <div class="ant-table-content">
                            <table>
                                <thead class="ant-table-thead">
                                    <tr>
                                        <th class="ant-table-cell ant-table-selection-column">
                                            <div class="ant-table-selection">
                                                <label class="ant-checkbox-wrapper">
                                                    <span class="ant-checkbox">
                                                        <input type="checkbox" class="ant-checkbox-input">
                                                        <span class="ant-checkbox-inner"></span>
                                                    </span>
                                                </label>
                                            </div>
                                        </th>
                                        <th class="ant-table-cell">Name</th>
                                        <th class="ant-table-cell">Age</th>
                                        <th class="ant-table-cell">Address</th>
                                    </tr>
                                </thead>
                                <tbody class="ant-table-tbody">
                                    <tr class="ant-table-row">
                                        <td class="ant-table-cell ant-table-selection-column">
                                            <label class="ant-checkbox-wrapper">
                                                <span class="ant-checkbox">
                                                    <input type="checkbox" class="ant-checkbox-input">
                                                    <span class="ant-checkbox-inner"></span>
                                                </span>
                                            </label>
                                        </td>
                                        <td class="ant-table-cell">John Brown</td>
                                        <td class="ant-table-cell">32</td>
                                        <td class="ant-table-cell">New York No. 1 Lake Park</td>
                                    </tr>
                                    <tr class="ant-table-row">
                                        <td class="ant-table-cell ant-table-selection-column">
                                            <label class="ant-checkbox-wrapper">
                                                <span class="ant-checkbox">
                                                    <input type="checkbox" class="ant-checkbox-input">
                                                    <span class="ant-checkbox-inner"></span>
                                                </span>
                                            </label>
                                        </td>
                                        <td class="ant-table-cell">Jim Green</td>
                                        <td class="ant-table-cell">42</td>
                                        <td class="ant-table-cell">London No. 1 Lake Park</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            """;

        String result = HtmlToMarkdownConverter.convert(antDesignSelectionTableHtml, HtmlConversionMode.LOOSE);

        // Verify table structure (selection column should be filtered out)
        assertTrue(result.contains("| Name | Age | Address |"));
        assertTrue(result.contains("|---|---|---|"));
        assertTrue(result.contains("| John Brown | 32 | New York No. 1 Lake Park |"));
        assertTrue(result.contains("| Jim Green | 42 | London No. 1 Lake Park |"));

        // Verify selection column content is not included
        assertFalse(result.contains("checkbox"));
    }

    @Test
    void testAntDesignTableWithExpandable() {
        String antDesignExpandableTableHtml = """
            <div class="ant-table-wrapper">
                <div class="ant-table">
                    <div class="ant-table-container">
                        <div class="ant-table-content">
                            <table>
                                <thead class="ant-table-thead">
                                    <tr>
                                        <th class="ant-table-cell ant-table-row-expand-icon-cell"></th>
                                        <th class="ant-table-cell">Name</th>
                                        <th class="ant-table-cell">Platform</th>
                                        <th class="ant-table-cell">Version</th>
                                    </tr>
                                </thead>
                                <tbody class="ant-table-tbody">
                                    <tr class="ant-table-row">
                                        <td class="ant-table-cell ant-table-row-expand-icon-cell">
                                            <button class="ant-table-row-expand-icon ant-table-row-expand-icon-collapsed">
                                                <span class="ant-table-row-expand-icon-spaced"></span>
                                            </button>
                                        </td>
                                        <td class="ant-table-cell">Screen</td>
                                        <td class="ant-table-cell">iOS</td>
                                        <td class="ant-table-cell">10.3.4.5654</td>
                                    </tr>
                                    <tr class="ant-table-row">
                                        <td class="ant-table-cell ant-table-row-expand-icon-cell">
                                            <button class="ant-table-row-expand-icon ant-table-row-expand-icon-collapsed">
                                                <span class="ant-table-row-expand-icon-spaced"></span>
                                            </button>
                                        </td>
                                        <td class="ant-table-cell">Screen</td>
                                        <td class="ant-table-cell">Android</td>
                                        <td class="ant-table-cell">2.3.3</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            """;

        String result = HtmlToMarkdownConverter.convert(antDesignExpandableTableHtml, HtmlConversionMode.LOOSE);

        // Verify table structure (expand column should be filtered out)
        assertTrue(result.contains("| Name | Platform | Version |"));
        assertTrue(result.contains("|---|---|---|"));
        assertTrue(result.contains("| Screen | iOS | 10.3.4.5654 |"));
        assertTrue(result.contains("| Screen | Android | 2.3.3 |"));

        // Verify expand icon content is not included
        assertFalse(result.contains("expand"));
    }

    @Test
    void testElementUITable() {
        String elementUITableHtml = """
            <div class="el-table el-table--fit el-table--enable-row-hover el-table--enable-row-transition">
                <div class="el-table__header-wrapper">
                    <table cellspacing="0" cellpadding="0" border="0" class="el-table__header">
                        <thead>
                            <tr>
                                <th class="el-table__cell">Date</th>
                                <th class="el-table__cell">Name</th>
                                <th class="el-table__cell">Status</th>
                                <th class="el-table__cell">Tags</th>
                                <th class="el-table__cell">Operations</th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="el-table__body-wrapper">
                    <table cellspacing="0" cellpadding="0" border="0" class="el-table__body">
                        <tbody>
                            <tr class="el-table__row">
                                <td class="el-table__cell">2016-05-02</td>
                                <td class="el-table__cell">John Brown</td>
                                <td class="el-table__cell">
                                    <span class="el-tag el-tag--success el-tag--light">Active</span>
                                </td>
                                <td class="el-table__cell">
                                    <span class="el-tag el-tag--primary">Developer</span>
                                    <span class="el-tag el-tag--info">Nice</span>
                                </td>
                                <td class="el-table__cell">
                                    <button class="el-button el-button--primary el-button--small">Edit</button>
                                    <button class="el-button el-button--danger el-button--small">Delete</button>
                                </td>
                            </tr>
                            <tr class="el-table__row">
                                <td class="el-table__cell">2016-05-04</td>
                                <td class="el-table__cell">Jim Green</td>
                                <td class="el-table__cell">
                                    <span class="el-tag el-tag--warning el-tag--light">Pending</span>
                                </td>
                                <td class="el-table__cell">
                                    <span class="el-tag el-tag--danger">Loser</span>
                                </td>
                                <td class="el-table__cell">
                                    <button class="el-button el-button--primary el-button--small">Edit</button>
                                    <button class="el-button el-button--danger el-button--small">Delete</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            """;

        String result = HtmlToMarkdownConverter.convert(elementUITableHtml, HtmlConversionMode.LOOSE);

        // Verify table structure
        assertTrue(result.contains("| Date | Name | Status | Tags | Operations |"));
        assertTrue(result.contains("|---|---|---|---|---|"));
        assertTrue(result.contains("| 2016-05-02 | John Brown | Active | Developer Nice | Edit Delete |"));
        assertTrue(result.contains("| 2016-05-04 | Jim Green | Pending | Loser | Edit Delete |"));
    }

    @Test
    void testVuetifyTable() {
        String vuetifyTableHtml = """
            <div class="v-data-table v-data-table--density-default v-theme--light">
                <div class="v-data-table__wrapper">
                    <table class="v-table v-table--density-default v-theme--light">
                        <thead>
                            <tr>
                                <th class="v-data-table-header__content">Name</th>
                                <th class="v-data-table-header__content">Age</th>
                                <th class="v-data-table-header__content">Status</th>
                                <th class="v-data-table-header__content">Tags</th>
                                <th class="v-data-table-header__content">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="v-data-table-row">
                                <td class="v-data-table-column">John Brown</td>
                                <td class="v-data-table-column">32</td>
                                <td class="v-data-table-column">
                                    <div class="v-chip v-chip--density-default v-theme--light v-chip--size-default">
                                        <span class="v-chip__content">Active</span>
                                    </div>
                                </td>
                                <td class="v-data-table-column">
                                    <div class="v-chip v-chip--density-default v-theme--light v-chip--size-default">
                                        <span class="v-chip__content">Developer</span>
                                    </div>
                                    <div class="v-chip v-chip--density-default v-theme--light v-chip--size-default">
                                        <span class="v-chip__content">Nice</span>
                                    </div>
                                </td>
                                <td class="v-data-table-column">
                                    <button class="v-btn v-btn--density-default v-btn--size-default v-btn--variant-elevated v-theme--light">
                                        <span class="v-btn__content">Edit</span>
                                    </button>
                                    <button class="v-btn v-btn--density-default v-btn--size-default v-btn--variant-elevated v-theme--light">
                                        <span class="v-btn__content">Delete</span>
                                    </button>
                                </td>
                            </tr>
                            <tr class="v-data-table-row">
                                <td class="v-data-table-column">Jim Green</td>
                                <td class="v-data-table-column">42</td>
                                <td class="v-data-table-column">
                                    <div class="v-chip v-chip--density-default v-theme--light v-chip--size-default">
                                        <span class="v-chip__content">Pending</span>
                                    </div>
                                </td>
                                <td class="v-data-table-column">
                                    <div class="v-chip v-chip--density-default v-theme--light v-chip--size-default">
                                        <span class="v-chip__content">Loser</span>
                                    </div>
                                </td>
                                <td class="v-data-table-column">
                                    <button class="v-btn v-btn--density-default v-btn--size-default v-btn--variant-elevated v-theme--light">
                                        <span class="v-btn__content">Edit</span>
                                    </button>
                                    <button class="v-btn v-btn--density-default v-btn--size-default v-btn--variant-elevated v-theme--light">
                                        <span class="v-btn__content">Delete</span>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            """;

        String result = HtmlToMarkdownConverter.convert(vuetifyTableHtml, HtmlConversionMode.LOOSE);

        // Verify table structure
        assertTrue(result.contains("| Name | Age | Status | Tags | Actions |"));
        assertTrue(result.contains("|---|---|---|---|---|"));
        assertTrue(result.contains("| John Brown | 32 | Active | Developer Nice | Edit Delete |"));
        assertTrue(result.contains("| Jim Green | 42 | Pending | Loser | Edit Delete |"));
    }

    @Test
    void testComplexNestedTable() {
        String complexTableHtml = """
            <div class="table-container">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Details</th>
                            <th>Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <strong>Laptop Pro</strong><br>
                                <small>Model: XYZ-123</small>
                            </td>
                            <td>
                                <ul>
                                    <li>16GB RAM</li>
                                    <li>512GB SSD</li>
                                    <li>Intel i7</li>
                                </ul>
                            </td>
                            <td>
                                <span class="price">$1,299.99</span><br>
                                <small class="text-muted">Free shipping</small>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            """;
        
        String result = HtmlToMarkdownConverter.convert(complexTableHtml, HtmlConversionMode.LOOSE);
        
        // Verify table structure is preserved
        assertTrue(result.contains("| Product | Details | Price |"));
        assertTrue(result.contains("|---|---|---|"));
        assertTrue(result.contains("Laptop Pro"));
        assertTrue(result.contains("16GB RAM"));
        assertTrue(result.contains("$1,299.99"));
    }

    @Test
    void testTableWithEscapedPipes() {
        String tableWithPipesHtml = """
            <table class="table">
                <thead>
                    <tr>
                        <th>Command</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>grep "pattern" | sort</td>
                        <td>Search and sort results</td>
                    </tr>
                    <tr>
                        <td>cat file.txt | wc -l</td>
                        <td>Count lines in file</td>
                    </tr>
                </tbody>
            </table>
            """;

        String result = HtmlToMarkdownConverter.convert(tableWithPipesHtml, HtmlConversionMode.LOOSE);

        // Verify pipes are properly escaped
        assertTrue(result.contains("| Command | Description |"));
        assertTrue(result.contains("grep \"pattern\" \\| sort"));
        assertTrue(result.contains("cat file.txt \\| wc -l"));
    }

    @Test
    void testEnhancedTablePerformance() {
        // Create a large Bootstrap table for performance testing
        StringBuilder largeTableHtml = new StringBuilder();
        largeTableHtml.append("""
            <div class="table-responsive">
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
            """);

        // Add 1000 rows
        for (int i = 1; i <= 1000; i++) {
            largeTableHtml.append(String.format("""
                        <tr>
                            <td>%d</td>
                            <td>User %d</td>
                            <td><EMAIL></td>
                            <td><span class="badge badge-success">Active</span></td>
                            <td><button class="btn btn-primary">Edit</button></td>
                        </tr>
                """, i, i, i));
        }

        largeTableHtml.append("""
                    </tbody>
                </table>
            </div>
            """);

        // Measure conversion time
        long startTime = System.nanoTime();
        String result = HtmlToMarkdownConverter.convert(largeTableHtml.toString(), HtmlConversionMode.LOOSE);
        long endTime = System.nanoTime();

        double durationMs = (endTime - startTime) / 1_000_000.0;

        // Verify the result contains expected content
        assertTrue(result.contains("| ID | Name | Email | Status | Actions |"));
        assertTrue(result.contains("| 1 | User 1 | <EMAIL> | Active | Edit |"));
        assertTrue(result.contains("| 1000 | User 1000 | <EMAIL> | Active | Edit |"));

        // Performance assertion - should process 1000 rows in reasonable time
        assertTrue(durationMs < 1000, "Processing 1000 table rows should take less than 1 second, took: " + durationMs + "ms");

        System.out.println("Enhanced table processing performance: " + durationMs + "ms for 1000 rows");
    }

    @Test
    void testFrameworkDetectionAccuracy() {
        // Test that different frameworks are correctly detected and processed

        // Bootstrap detection
        String bootstrapHtml = "<table class=\"table table-striped\"><tr><th>Test</th></tr><tr><td>Data</td></tr></table>";
        String bootstrapResult = HtmlToMarkdownConverter.convert(bootstrapHtml, HtmlConversionMode.LOOSE);
        assertTrue(bootstrapResult.contains("| Test |"));

        // Tailwind detection
        String tailwindHtml = "<table class=\"table-auto border-collapse\"><tr><th>Test</th></tr><tr><td>Data</td></tr></table>";
        String tailwindResult = HtmlToMarkdownConverter.convert(tailwindHtml, HtmlConversionMode.LOOSE);
        assertTrue(tailwindResult.contains("| Test |"));

        // DataTable detection
        String datatableHtml = "<div class=\"dataTables_wrapper\"><table class=\"display dataTable\"><tr><th class=\"sorting\">Test</th></tr><tr><td>Data</td></tr></table></div>";
        String datatableResult = HtmlToMarkdownConverter.convert(datatableHtml, HtmlConversionMode.LOOSE);
        assertTrue(datatableResult.contains("| Test |"));

        // Material Design detection
        String materialHtml = "<div class=\"mdc-data-table\"><table class=\"mdc-data-table__table\"><tr><th class=\"mdc-data-table__header-cell\">Test</th></tr><tr><td class=\"mdc-data-table__cell\">Data</td></tr></table></div>";
        String materialResult = HtmlToMarkdownConverter.convert(materialHtml, HtmlConversionMode.LOOSE);
        assertTrue(materialResult.contains("| Test |"));

        // Ant Design detection
        String antDesignHtml = "<div class=\"ant-table-wrapper\"><table class=\"ant-table\"><tr><th class=\"ant-table-cell\">Test</th></tr><tr><td class=\"ant-table-cell\">Data</td></tr></table></div>";
        String antDesignResult = HtmlToMarkdownConverter.convert(antDesignHtml, HtmlConversionMode.LOOSE);
        assertTrue(antDesignResult.contains("| Test |"));

        // Element UI detection
        String elementUIHtml = "<div class=\"el-table\"><table class=\"el-table__header\"><tr><th class=\"el-table__cell\">Test</th></tr><tr><td class=\"el-table__cell\">Data</td></tr></table></div>";
        String elementUIResult = HtmlToMarkdownConverter.convert(elementUIHtml, HtmlConversionMode.LOOSE);
        assertTrue(elementUIResult.contains("| Test |"));

        // Vuetify detection
        String vuetifyHtml = "<div class=\"v-data-table\"><table class=\"v-table\"><tr><th class=\"v-data-table-header__content\">Test</th></tr><tr><td class=\"v-data-table-column\">Data</td></tr></table></div>";
        String vuetifyResult = HtmlToMarkdownConverter.convert(vuetifyHtml, HtmlConversionMode.LOOSE);
        assertTrue(vuetifyResult.contains("| Test |"));

        // Standard table (no framework)
        String standardHtml = "<table><tr><th>Test</th></tr><tr><td>Data</td></tr></table>";
        String standardResult = HtmlToMarkdownConverter.convert(standardHtml, HtmlConversionMode.LOOSE);
        assertTrue(standardResult.contains("| Test |"));
    }
}
