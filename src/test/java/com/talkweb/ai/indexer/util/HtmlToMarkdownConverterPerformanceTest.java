package com.talkweb.ai.indexer.util;

import com.talkweb.ai.indexer.util.markdown.converter.ConverterRegistry;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Performance tests for the optimized HTML to Markdown converter
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class HtmlToMarkdownConverterPerformanceTest {
    
    private static final Logger logger = LoggerFactory.getLogger(HtmlToMarkdownConverterPerformanceTest.class);
    
    private String simpleHtml;
    private String complexHtml;
    private String largeHtml;
    
    @BeforeEach
    void setUp() {
        // Simple HTML for basic performance testing
        simpleHtml = "<html><body><h1>Title</h1><p>Simple paragraph with <strong>bold</strong> text.</p></body></html>";
        
        // Complex HTML with various elements
        complexHtml = """
            <html>
            <head><title>Test Document</title></head>
            <body>
                <h1>Main Title</h1>
                <p>This is a paragraph with <strong>bold</strong> and <em>italic</em> text.</p>
                
                <h2>Lists</h2>
                <ul>
                    <li>First item</li>
                    <li>Second item with <a href="http://example.com">link</a></li>
                    <li>Third item</li>
                </ul>
                
                <ol>
                    <li>Numbered item 1</li>
                    <li>Numbered item 2</li>
                </ol>
                
                <h3>Table</h3>
                <table>
                    <thead>
                        <tr><th>Name</th><th>Age</th><th>City</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>John</td><td>25</td><td>New York</td></tr>
                        <tr><td>Jane</td><td>30</td><td>London</td></tr>
                    </tbody>
                </table>
                
                <h3>Code</h3>
                <p>Inline <code>code</code> example.</p>
                <pre><code class="language-java">
                public class Example {
                    public static void main(String[] args) {
                        System.out.println("Hello, World!");
                    }
                }
                </code></pre>
                
                <blockquote>
                    <p>This is a blockquote with multiple lines.</p>
                    <p>Second paragraph in blockquote.</p>
                </blockquote>
                
                <p>Image: <img src="test.jpg" alt="Test Image" title="Test"></p>
                
                <hr>
                
                <div>
                    <p>Nested content in div</p>
                    <span>Inline content</span>
                </div>
            </body>
            </html>
            """;
        
        // Large HTML for stress testing
        StringBuilder largeBuilder = new StringBuilder();
        largeBuilder.append("<html><body>");
        
        for (int i = 0; i < 100; i++) {
            largeBuilder.append("<h2>Section ").append(i + 1).append("</h2>");
            largeBuilder.append("<p>This is paragraph ").append(i + 1).append(" with some <strong>bold</strong> text.</p>");
            
            largeBuilder.append("<ul>");
            for (int j = 0; j < 10; j++) {
                largeBuilder.append("<li>List item ").append(j + 1).append(" in section ").append(i + 1).append("</li>");
            }
            largeBuilder.append("</ul>");
            
            if (i % 10 == 0) {
                largeBuilder.append("<table>");
                largeBuilder.append("<tr><th>Column 1</th><th>Column 2</th><th>Column 3</th></tr>");
                for (int k = 0; k < 5; k++) {
                    largeBuilder.append("<tr><td>Data ").append(k + 1).append("</td><td>Value ").append(k + 1)
                              .append("</td><td>Info ").append(k + 1).append("</td></tr>");
                }
                largeBuilder.append("</table>");
            }
        }
        
        largeBuilder.append("</body></html>");
        largeHtml = largeBuilder.toString();
    }
    
    @Test
    void testSimpleConversionPerformance() {
        logger.info("Testing simple HTML conversion performance");
        
        // Warm up
        for (int i = 0; i < 10; i++) {
            HtmlToMarkdownConverter.convert(simpleHtml);
        }
        
        // Measure performance
        long startTime = System.nanoTime();
        int iterations = 1000;
        
        for (int i = 0; i < iterations; i++) {
            String result = HtmlToMarkdownConverter.convert(simpleHtml);
            assertNotNull(result);
            assertFalse(result.trim().isEmpty());
        }
        
        long endTime = System.nanoTime();
        long durationMs = (endTime - startTime) / 1_000_000;
        double avgTimeMs = (double) durationMs / iterations;
        
        logger.info("Simple conversion: {} iterations in {}ms (avg: {:.2f}ms per conversion)", 
                   iterations, durationMs, avgTimeMs);
        
        // Performance assertion - should be fast
        assertTrue(avgTimeMs < 5.0, "Average conversion time should be less than 5ms");
    }
    
    @Test
    void testComplexConversionPerformance() {
        logger.info("Testing complex HTML conversion performance");
        
        // Warm up
        for (int i = 0; i < 5; i++) {
            HtmlToMarkdownConverter.convert(complexHtml);
        }
        
        // Measure performance
        long startTime = System.nanoTime();
        int iterations = 100;
        
        for (int i = 0; i < iterations; i++) {
            String result = HtmlToMarkdownConverter.convert(complexHtml);
            assertNotNull(result);
            assertFalse(result.trim().isEmpty());
            
            // Verify some expected content
            assertTrue(result.contains("# Main Title"));
            assertTrue(result.contains("- First item"));
            assertTrue(result.contains("| Name | Age | City |"));
        }
        
        long endTime = System.nanoTime();
        long durationMs = (endTime - startTime) / 1_000_000;
        double avgTimeMs = (double) durationMs / iterations;
        
        logger.info("Complex conversion: {} iterations in {}ms (avg: {:.2f}ms per conversion)", 
                   iterations, durationMs, avgTimeMs);
        
        // Performance assertion
        assertTrue(avgTimeMs < 20.0, "Average complex conversion time should be less than 20ms");
    }
    
    @Test
    void testLargeDocumentPerformance() {
        logger.info("Testing large HTML document conversion performance");
        logger.info("Large HTML size: {} characters", largeHtml.length());
        
        // Warm up
        HtmlToMarkdownConverter.convert(largeHtml);
        
        // Measure performance
        long startTime = System.nanoTime();
        int iterations = 10;
        
        for (int i = 0; i < iterations; i++) {
            String result = HtmlToMarkdownConverter.convert(largeHtml);
            assertNotNull(result);
            assertFalse(result.trim().isEmpty());
        }
        
        long endTime = System.nanoTime();
        long durationMs = (endTime - startTime) / 1_000_000;
        double avgTimeMs = (double) durationMs / iterations;
        
        logger.info("Large document conversion: {} iterations in {}ms (avg: {:.2f}ms per conversion)", 
                   iterations, durationMs, avgTimeMs);
        
        // Performance assertion - should handle large documents reasonably fast
        assertTrue(avgTimeMs < 500.0, "Average large document conversion time should be less than 500ms");
    }
    
    @Test
    void testConcurrentConversionPerformance() throws Exception {
        logger.info("Testing concurrent HTML conversion performance");
        
        int threadCount = 4;
        int conversionsPerThread = 50;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        long startTime = System.nanoTime();
        
        for (int t = 0; t < threadCount; t++) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                for (int i = 0; i < conversionsPerThread; i++) {
                    String result = HtmlToMarkdownConverter.convert(complexHtml);
                    assertNotNull(result);
                    assertFalse(result.trim().isEmpty());
                }
            }, executor);
            futures.add(future);
        }
        
        // Wait for all threads to complete
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
        
        long endTime = System.nanoTime();
        long durationMs = (endTime - startTime) / 1_000_000;
        int totalConversions = threadCount * conversionsPerThread;
        double avgTimeMs = (double) durationMs / totalConversions;
        
        logger.info("Concurrent conversion: {} threads, {} conversions total in {}ms (avg: {:.2f}ms per conversion)", 
                   threadCount, totalConversions, durationMs, avgTimeMs);
        
        executor.shutdown();
        assertTrue(executor.awaitTermination(5, TimeUnit.SECONDS));
        
        // Performance assertion - concurrent access should be efficient
        assertTrue(avgTimeMs < 30.0, "Average concurrent conversion time should be less than 30ms");
    }
    
    @Test
    void testConverterRegistryCachePerformance() {
        logger.info("Testing converter registry cache performance");
        
        ConverterRegistry registry = new ConverterRegistry();
        
        // First run to populate cache
        long startTime = System.nanoTime();
        for (int i = 0; i < 100; i++) {
            HtmlToMarkdownConverter.convert(complexHtml, HtmlConversionMode.LOOSE, registry);
        }
        long firstRunTime = (System.nanoTime() - startTime) / 1_000_000;
        
        // Second run should benefit from cache
        startTime = System.nanoTime();
        for (int i = 0; i < 100; i++) {
            HtmlToMarkdownConverter.convert(complexHtml, HtmlConversionMode.LOOSE, registry);
        }
        long secondRunTime = (System.nanoTime() - startTime) / 1_000_000;
        
        logger.info("Cache performance - First run: {}ms, Second run: {}ms", firstRunTime, secondRunTime);
        logger.info("Cache stats: {}", registry.getCacheStats());
        
        // Cache should provide some performance benefit
        assertTrue(secondRunTime <= firstRunTime * 1.1, "Cached run should not be significantly slower");
    }
}
