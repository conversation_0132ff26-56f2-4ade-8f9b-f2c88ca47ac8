package com.talkweb.ai.indexer.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class JsonUtilsTest {

    @TempDir
    Path tempDir;
    
    @Test
    void testLoadJson() throws IOException {
        // 创建JSON文件
        String json = "{\n" +
                "  \"key1\": \"value1\",\n" +
                "  \"key2\": 123,\n" +
                "  \"nested\": {\n" +
                "    \"nestedKey1\": \"nestedValue1\",\n" +
                "    \"nestedKey2\": 456\n" +
                "  }\n" +
                "}";
        
        Path jsonFile = tempDir.resolve("test.json");
        Files.writeString(jsonFile, json);
        
        // 加载JSON
        Map<String, Object> result = JsonUtils.loadJson(jsonFile.toFile());
        
        // 验证结果
        assertEquals("value1", result.get("key1"));
        assertEquals(123, result.get("key2"));
        assertTrue(result.get("nested") instanceof Map);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> nested = (Map<String, Object>) result.get("nested");
        assertEquals("nestedValue1", nested.get("nestedKey1"));
        assertEquals(456, nested.get("nestedKey2"));
    }
    
    @Test
    void testLoadEmptyJson() throws IOException {
        // 创建空JSON文件
        Path jsonFile = tempDir.resolve("empty.json");
        Files.writeString(jsonFile, "{}");
        
        // 加载JSON
        Map<String, Object> result = JsonUtils.loadJson(jsonFile.toFile());
        
        // 验证结果
        assertTrue(result.isEmpty());
    }
    
    @Test
    void testLoadInvalidJson() throws IOException {
        // 创建无效JSON文件
        String json = "{ invalid: json }";
        Path jsonFile = tempDir.resolve("invalid.json");
        Files.writeString(jsonFile, json);
        
        // 加载JSON应该抛出异常
        assertThrows(Exception.class, () -> JsonUtils.loadJson(jsonFile.toFile()));
    }
    
    @Test
    void testFlattenJsonMap() throws IOException {
        // 创建嵌套Map
        String json = "{\n" +
                "  \"key1\": \"value1\",\n" +
                "  \"key2\": 123,\n" +
                "  \"nested\": {\n" +
                "    \"nestedKey1\": \"nestedValue1\",\n" +
                "    \"nestedKey2\": 456,\n" +
                "    \"deepNested\": {\n" +
                "      \"deepKey\": \"deepValue\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
        
        Path jsonFile = tempDir.resolve("nested.json");
        Files.writeString(jsonFile, json);
        
        // 加载JSON
        Map<String, Object> jsonMap = JsonUtils.loadJson(jsonFile.toFile());
        
        // 扁平化Map
        Map<String, String> flattened = JsonUtils.flattenJsonMap(jsonMap);
        
        // 验证结果
        assertEquals("value1", flattened.get("key1"));
        assertEquals("123", flattened.get("key2"));
        assertEquals("nestedValue1", flattened.get("nested.nestedKey1"));
        assertEquals("456", flattened.get("nested.nestedKey2"));
        assertEquals("deepValue", flattened.get("nested.deepNested.deepKey"));
    }
    
    @Test
    void testFlattenJsonMapWithNullValues() {
        // 创建带空值的Map
        Map<String, Object> jsonMap = new HashMap<>();
        jsonMap.put("key1", "value1");
        jsonMap.put("key2", null);
        
        Map<String, Object> nestedMap = new HashMap<>();
        nestedMap.put("nestedKey1", "nestedValue1");
        nestedMap.put("nestedKey2", null);
        
        jsonMap.put("nested", nestedMap);
        
        // 扁平化Map
        Map<String, String> flattened = JsonUtils.flattenJsonMap(jsonMap);
        
        // 验证结果
        assertEquals("value1", flattened.get("key1"));
        assertEquals("", flattened.get("key2"));
        assertEquals("nestedValue1", flattened.get("nested.nestedKey1"));
        assertEquals("", flattened.get("nested.nestedKey2"));
    }
}