# 代码库概要

本文档提供智能文档索引器项目的高层设计、架构和代码结构说明。

## 1. 系统架构

本系统采用基于插件的模块化架构，以确保高度的可扩展性和可维护性。核心是处理引擎和插件注册表，通过 SPI 机制支持动态扩展各种文档处理能力。

### 1.1 主要组件

- **`CommandLineInterface`**: 使用 Picocli 构建，负责解析和验证用户输入的命令和参数，支持批处理配置。
- **`FileScanner`**: 遍历指定的输入目录，支持递归扫描与多格式过滤，并生成待处理文件队列。
- **`ProcessingPipeline`**: 文档处理流水线，协调不同阶段的处理插件，支持前处理、主处理和后处理阶段。
- **`PluginRegistry`**: 插件注册表，负责发现、加载和管理插件的生命周期。
- **`DocumentProcessor` (接口)**: 定义所有处理器的标准契约，包含处理方法和元数据支持。
- **具体处理器实现**:
    - `TxtProcessor`: 处理纯文本文档。
    - `MarkdownProcessor`: 处理 Markdown 文档。
    - `HtmlProcessor`: 处理 HTML 文档。
    - `PdfProcessor`: 处理 PDF 文档。
    - `DocxProcessor`, `XlsxProcessor`, `PptxProcessor`: 处理 Office 文档。
- **`MetadataExtractor`**: 负责从源文件中提取元数据，并标准化管理。
- **`ChunkingStrategy`**: 内容分块策略，支持固定大小、语义分块等多种算法。
- **`VectorEmbeddingService`**: 向量嵌入服务，集成多种嵌入模型。
- **`VectorDatabaseConnector`**: 向量数据库连接适配器，支持 Milvus/Pinecone 等。
- **`AiService`**: AI 增强服务，集成 Spring AI，提供摘要生成、实体识别等功能。

### 1.2 包结构

### 1.2 建议包结构

```
com.talkweb.ai.indexer 
├── cli // 命令行接口与参数解析 
├── core // 核心处理引擎与插件框架 
├── pipeline // 处理流水线 
├── plugin // 插件机制 
├── config // 配置管理 
├── processor // 各类文档处理器 
├── service // 业务服务 
├── model // 数据模型 
├── util // 工具类 
└── DocConverterApplication.java // Spring Boot 启动类
```

### 1.3 关键交互流程

1. **初始化流程**:
   - `DocConverterApplication` 启动，初始化 Spring 上下文
   - `PluginRegistry` 扫描并加载可用插件
   - `CommandLineInterface` 解析命令行参数或配置文件

2. **文档处理流程**:
   - `FileScanner` 扫描并过滤文件，创建处理队列
   - `ProcessingPipeline` 协调处理流程：
     - 前处理阶段：文档格式检测、资源限制检查
     - 主处理阶段：调用合适的 `DocumentProcessor` 提取内容
     - 后处理阶段：元数据提取、内容规范化

3. **内容增强流程**:
   - 通过 `AiService` 调用 AI 模型进行文档分析：
     - 自动摘要生成
     - 关键实体提取
     - 知识图谱构建

4. **向量化流程**:
   - `ChunkingStrategy` 将文档内容分块
   - `VectorEmbeddingService` 生成文本块的向量表示
   - `VectorDatabaseConnector` 将向量存入向量数据库

5. **增量处理流程**:
   - 检测文档变更
   - 仅处理变更部分
   - 更新索引与向量库

## 2. 技术栈

- **核心框架**: Java 21, Spring Boot 3.5.2
- **AI 支持**: Spring AI 1.0
- **命令行**: Picocli
- **文档处理**: Apache POI, PDFBox, Jsoup, Tesseract OCR, CommonMark
- **并发处理**: Project Reactor, Java Virtual Threads
- **监控**: Micrometer, Prometheus, Grafana
- **测试**: JUnit 5, AssertJ, Mockito

## 3. 当前开发状态

- 已完成项目初始化和基本结构搭建
- 正在设计插件架构框架和核心接口
- 即将开始实现命令行接口和文件扫描器

## 4. 近期变更

- 项目规格说明 ([projectSpec.md](docs/projectSpec.md:0:0-0:0)) 已更新，增加了性能、安全性和扩展性等细化需求
- 项目路线图 ([projectRoadmap.md](docs/projectRoadmap.md:0:0-0:0)) 已同步更新，调整为六个主要开发阶段
- 当前任务 ([currentTask.md](docs/currentTask.md:0:0-0:0)) 已更新，明确了下一步开发重点