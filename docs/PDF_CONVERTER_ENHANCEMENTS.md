# PDF转Markdown转换器增强功能

## 概述

本文档描述了对PDF转Markdown转换器的重大改进，实现了更好的兼容性、结构保留和页面分割功能。

## 主要改进

### 1. PDF文档兼容性增强

#### 改进内容
- **加密PDF支持**: 自动检测加密PDF并支持密码解锁
- **损坏文件处理**: 增强的错误恢复机制，能处理部分损坏的PDF文件
- **版本兼容性**: 支持更多PDF版本和格式
- **内存优化**: 改进大文件处理，减少内存占用

#### 技术实现
```java
// 增强的PDF加载逻辑
private PDDocument loadPdfWithRetry(File inputFile) throws PdfConversionException {
    try {
        return Loader.loadPDF(inputFile);
    } catch (InvalidPasswordException e) {
        if (config.isHandleEncrypted() && !config.getDefaultPassword().isEmpty()) {
            return Loader.loadPDF(inputFile, config.getDefaultPassword());
        }
        throw PdfConversionException.encryptedFile(fileName);
    } catch (IOException e) {
        // 损坏文件恢复逻辑
        // ...
    }
}
```

### 2. 原始结构保留

#### 改进内容
- **标题层次检测**: 基于字体大小、样式和位置的智能标题识别
- **段落结构**: 保留原始段落分隔和缩进
- **列表格式**: 支持有序和无序列表的准确转换
- **表格处理**: 增强的表格检测和Markdown格式化
- **文本样式**: 保留粗体、斜体等文本格式

#### 技术实现
```java
// 增强的结构分析
private boolean isHeading(EnhancedTextLine line, float bodyFontSize) {
    return line.isBold || 
           line.fontSize > bodyFontSize * 1.15 ||
           HEADING_PATTERN.matcher(line.text.trim()).matches();
}

// 文本格式应用
private String applyTextFormatting(String text, EnhancedTextLine line) {
    if (line.isBold && line.isItalic) {
        return "***" + text + "***";
    } else if (line.isBold) {
        return "**" + text + "**";
    } else if (line.isItalic) {
        return "*" + text + "*";
    }
    return text;
}
```

### 3. 页面分割转换

#### 改进内容
- **独立页面文件**: 每个PDF页面生成单独的Markdown文件
- **页面编号**: 自动添加页面标题和编号
- **目录生成**: 创建包含所有页面的汇总文件
- **元数据保留**: 在每个页面文件中包含文档元数据

#### 文件结构
```
document_pages/
├── page_001.md
├── page_002.md
├── page_003.md
└── _all_pages.md
```

#### 技术实现
```java
// 页面分割转换
private ConversionResult convertToSeparatePages(PDDocument document, File inputFile) {
    for (int pageNum = 0; pageNum < pagesToProcess; pageNum++) {
        EnhancedMarkdownPdfTextStripper stripper = 
            new EnhancedMarkdownPdfTextStripper(pageNum + 1, pageNum + 1);
        String pageContent = stripper.getText(document);
        
        String pageFileName = String.format("page_%03d.md", pageNum + 1);
        Path pageFilePath = outputDir.resolve(pageFileName);
        
        String pageHeader = String.format("# Page %d\n\n", pageNum + 1);
        String fullPageContent = pageHeader + pageContent;
        
        Files.write(pageFilePath, fullPageContent.getBytes());
    }
}
```

### 4. 页面元素和层次结构处理

#### 改进内容
- **字体分析**: 详细的字体名称、大小、样式检测
- **位置分析**: 精确的文本位置和间距计算
- **URL检测**: 自动识别和格式化URL链接
- **图像占位符**: 为图像位置添加占位符标记

#### 数据结构
```java
private static class EnhancedTextLine extends TextLine {
    final boolean isItalic;
    final String fontName;
    
    EnhancedTextLine(String text, float fontSize, float y, 
                    boolean isBold, boolean isItalic, String fontName) {
        super(text, fontSize, y, isBold);
        this.isItalic = isItalic;
        this.fontName = fontName;
    }
}
```

## 配置系统

### 配置选项

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `splitByPages` | boolean | true | 是否分页转换 |
| `preserveStructure` | boolean | true | 是否保留结构 |
| `extractImages` | boolean | false | 是否提取图像 |
| `handleEncrypted` | boolean | true | 是否处理加密文件 |
| `defaultPassword` | String | "" | 默认密码 |
| `maxPages` | int | MAX_VALUE | 最大页面数 |
| `includeMetadata` | boolean | true | 是否包含元数据 |

### 配置方式

#### 1. 编程配置
```java
PdfToMarkdownConverter.PdfConversionConfig config = 
    PdfConversionConfigBuilder.newBuilder()
        .splitByPages(true)
        .preserveStructure(true)
        .maxPages(50)
        .build();

converter.configure(config);
```

#### 2. 属性文件配置
```properties
pdf.splitByPages=true
pdf.preserveStructure=true
pdf.maxPages=50
pdf.includeMetadata=true
```

```java
Properties props = loadProperties();
PdfToMarkdownConverter.PdfConversionConfig config = 
    PdfConversionConfigBuilder.fromProperties(props).build();
```

#### 3. 预设配置
```java
// 高质量配置
PdfConversionConfigBuilder.createHighQualityConfig()

// 快速配置
PdfConversionConfigBuilder.createFastConfig()

// 批处理配置
PdfConversionConfigBuilder.createBatchConfig()
```

## 错误处理

### 异常类型

| 异常类型 | 描述 | 可恢复 |
|----------|------|--------|
| `FILE_NOT_FOUND` | 文件不存在 | 否 |
| `CORRUPTED_FILE` | 文件损坏 | 否 |
| `ENCRYPTED_FILE` | 文件加密 | 否 |
| `PARSING_ERROR` | 解析错误 | 是 |
| `MEMORY_ERROR` | 内存不足 | 是 |
| `TIMEOUT_ERROR` | 处理超时 | 是 |

### 使用示例
```java
try {
    ConversionResult result = converter.convert(pdfFile);
} catch (ConversionException e) {
    if (e.getCause() instanceof PdfConversionException) {
        PdfConversionException pdfEx = (PdfConversionException) e.getCause();
        logger.error("PDF conversion failed: " + pdfEx.getDetailedMessage());
        
        if (pdfEx.isRecoverable()) {
            // 尝试恢复操作
        }
    }
}
```

## 性能优化

### 内存管理
- 流式处理大文件
- 及时释放PDF文档资源
- 分页处理减少内存峰值

### 处理速度
- 并发页面处理（可选）
- 缓存字体分析结果
- 优化正则表达式匹配

## 测试覆盖

### 测试类型
- 单元测试：核心功能测试
- 集成测试：完整转换流程测试
- 性能测试：大文件处理测试
- 错误处理测试：异常场景测试

### 测试用例
- 基本PDF转换
- 复杂结构PDF转换
- 加密PDF处理
- 损坏文件处理
- 配置验证
- 错误恢复

## 使用指南

### 基本使用
```java
// 创建转换器
PdfToMarkdownConverter converter = new PdfToMarkdownConverter(metadata);

// 配置（可选）
converter.configure(PdfConversionConfigBuilder.createHighQualityConfig());

// 初始化和启动
converter.init(context);
converter.start();

// 转换文件
ConversionResult result = converter.convert(pdfFile);

// 处理结果
if (result.isSuccess()) {
    System.out.println("转换成功: " + result.getMessage());
} else {
    System.err.println("转换失败: " + result.getMessage());
}

// 停止转换器
converter.stop();
```

### 批量处理
```java
PdfToMarkdownConverter converter = new PdfToMarkdownConverter(metadata);
converter.configure(PdfConversionConfigBuilder.createBatchConfig());

for (File pdfFile : pdfFiles) {
    try {
        ConversionResult result = converter.convert(pdfFile);
        // 处理结果
    } catch (ConversionException e) {
        // 记录错误，继续处理下一个文件
    }
}
```

## 总结

通过这些增强功能，PDF转Markdown转换器现在具备了：

1. **更强的兼容性** - 支持更多PDF格式和特殊情况
2. **更好的结构保留** - 准确保留文档的原始层次和格式
3. **灵活的输出选项** - 支持整体转换和页面分割
4. **完善的错误处理** - 详细的错误分类和恢复机制
5. **丰富的配置选项** - 适应不同使用场景的需求

这些改进使得转换器能够处理更复杂的PDF文档，并生成高质量的Markdown输出。
