# 技术栈

本文档记录了本项目的关键技术选型，详细的技术上下文请参考 [memory-bank/techContext.md](../memory-bank/techContext.md)。

## 核心框架与语言
- **核心语言**: Java 21
- **构建工具**: Maven
- **框架**: Spring Boot 3.5.3
- **架构模式**: 插件化架构 (Java SPI), 责任链模式

## AI 与机器学习
- **AI 集成**: Spring AI 1.0

## 命令行工具
- **CLI 解析**: Picocli 4.7.5

## 文档处理库
- **Microsoft Office**: Apache POI
- **PDF**: Apache PDFBox
- **HTML**: Jsoup
- **图片 OCR**: Tesseract OCR
- **Markdown 处理**: CommonMark

## 异步与并发
- **异步处理**: 虚拟线程 (Java 21) + CompletableFuture

## 测试与质量
- **测试框架**: JUnit 5, Mockito
- **代码质量**: Checkstyle, SpotBugs
